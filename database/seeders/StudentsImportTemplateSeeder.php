<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class StudentsImportTemplateSeeder extends Seeder
{
    public function run()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set headers
        $sheet->setCellValue('A1', 'name');
        $sheet->setCellValue('B1', 'department');
        $sheet->setCellValue('C1', 'program');
        $sheet->setCellValue('D1', 'year_of_enrollment');
        $sheet->setCellValue('E1', 'index_number');
        
        // Add some example data
        $sheet->setCellValue('A2', 'Faculty Developer');
        $sheet->setCellValue('B2', 'Environmental science');
        $sheet->setCellValue('C2', 'BSc Environmental science');
        $sheet->setCellValue('D2', date('Y'));
        $sheet->setCellValue('E2', '0321080010');
        
        // Auto size columns for better Excel display
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Create the templates directory if it doesn't exist
        $directory = public_path('templates');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
        
        // Save the file
        $writer = new Xlsx($spreadsheet);
        $writer->save($directory . '/students_import_template.xlsx');
        
        $this->command->info('Student import template created successfully at: ' . $directory . '/students_import_template.xlsx');
    }
}
