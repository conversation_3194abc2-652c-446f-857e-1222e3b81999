<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('outgoing_memos', function (Blueprint $table) {
            $table->id();
            $table->date('date_of_dispatch');
            $table->unsignedBigInteger('registry_no')->unique();
            $table->string('recipient', 100);
            $table->date('date_of_letter');
            $table->unsignedBigInteger('letter_no');
            $table->text('subject');
            $table->string('remark', 255)->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('outgoing_memos');
    }
};
