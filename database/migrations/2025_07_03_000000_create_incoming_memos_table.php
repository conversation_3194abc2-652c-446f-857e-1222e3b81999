<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('incoming_memos', function (Blueprint $table) {
            $table->id();
            $table->date('date_received');
            $table->string('registry_no', 50);
            $table->string('from_whom_received', 255);
            $table->date('date_of_letter');
            $table->string('letter_no', 50);
            $table->text('subject');
            $table->text('remark')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('incoming_memos');
    }
};
