<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cgpa', function (Blueprint $table) {
            $table->id();
            $table->string('index_number', 50)->unique();
            $table->string('name');
            $table->decimal('gpa', 3, 2)->default(0.00);
            $table->decimal('cgpa', 3, 2)->default(0.00);
            $table->integer('totalcredit')->default(0);
            $table->decimal('totalpoint', 10, 2)->default(0.00);
            $table->string('program', 100);
            $table->string('department', 100);
            $table->string('semester', 20);
            $table->year('year_of_enrollment');
            $table->string('email', 100)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cgpa');
    }
};
