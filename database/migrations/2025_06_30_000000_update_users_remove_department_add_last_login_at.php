<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove deprecated "department" column if present
            if (Schema::hasColumn('users', 'department')) {
                $table->dropColumn('department');
            }

            // Ensure we have a last_login_at timestamp column
            if (! Schema::hasColumn('users', 'last_login_at')) {
                $table->timestamp('last_login_at')->nullable()->after('email_verified_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Re-add department column if it was previously dropped
            if (! Schema::hasColumn('users', 'department')) {
                $table->string('department')->nullable()->after('role');
            }

            // Drop last_login_at if it was added by this migration
            if (Schema::hasColumn('users', 'last_login_at')) {
                $table->dropColumn('last_login_at');
            }
        });
    }
};
