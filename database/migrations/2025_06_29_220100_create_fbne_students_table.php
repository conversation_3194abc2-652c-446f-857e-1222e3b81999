<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fbne_students', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('department');
            $table->year('year_of_enrollment');
            $table->string('program');
            $table->string('index_number')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fbne_students');
    }
};
