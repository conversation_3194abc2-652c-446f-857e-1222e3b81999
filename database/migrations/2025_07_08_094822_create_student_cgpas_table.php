<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_cgpas', function (Blueprint $table) {
            $table->id();
            $table->string('index_number');
            $table->string('name');
            $table->decimal('gpa', 4, 2);
            $table->decimal('cgpa', 4, 2);
            $table->decimal('totalpoint', 10, 2);
            $table->decimal('totalcredit', 10, 2);
            $table->string('program');
            $table->string('department');
            $table->string('semester');
            $table->integer('year_of_enrollment');
            $table->string('email')->nullable();
            $table->softDeletes();
            $table->timestamps();
            
            // Add index for searchable fields
            $table->index('index_number');
            $table->index('name');
            $table->index('program');
            $table->index('department');
            $table->index('semester');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_cgpas');
    }
};
