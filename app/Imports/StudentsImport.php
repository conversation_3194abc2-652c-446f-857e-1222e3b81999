<?php

namespace App\Imports;

use App\Models\Student;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Validators\Failure;
use Illuminate\Validation\Rule;

class StudentsImport implements 
    ToModel, 
    WithHeadingRow, 
    WithValidation,
    SkipsOnError,
    SkipsOnFailure
{
    use SkipsErrors, SkipsFailures;

    private $rowCount = 0;
    private $importErrors = [];
    
    /**
     * Get the number of rows processed
     * 
     * @return int
     */
    public function getRowCount()
    {
        return $this->rowCount;
    }

    /**
     * @param array $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $this->rowCount++;
        
        // Skip if the row doesn't have required fields
        if (empty($row['name']) || empty($row['index_number'])) {
            $this->importErrors[] = "Row {$this->rowCount}: Missing required fields (name or index number)";
            return null;
        }

        // Check if student with this index number already exists
        $existingStudent = Student::where('index_number', $row['index_number'])->first();
        
        if ($existingStudent) {
            $this->errors[] = "Row {$this->rowCount}: Student with index number {$row['index_number']} already exists";
            return null;
        }

        return new Student([
            'name' => $row['name'],
            'department' => $row['department'] ?? null,
            'program' => $row['program'] ?? null,
            'year_of_enrollment' => $row['year_of_enrollment'] ?? date('Y'),
            'index_number' => $row['index_number'],
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|regex:/^[A-Za-z.\s]+$/u',
            'department' => 'required|string|max:255|regex:/^[A-Za-z\s-]+$/u',
            'program' => 'required|string|max:255|regex:/^[A-Za-z\s-]+$/u',
            'year_of_enrollment' => 'required|digits:4|integer|min:1900|max:' . date('Y'),
            'index_number' => 'required|digits:10|unique:fbne_students,index_number',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'name.regex' => 'The name field may only contain letters, spaces, and dots.',
            'year_of_enrollment.digits' => 'The year of enrollment must be exactly 4 digits.',
            'year_of_enrollment.min' => 'The year of enrollment must be 1900 or later.',
            'year_of_enrollment.max' => 'The year of enrollment cannot be in the future.',
            'index_number.digits' => 'The index number must be exactly 10 digits.',
            'index_number.unique' => 'A student with this index number already exists.',
            'department.regex' => 'The department name contains invalid characters.',
            'program.regex' => 'The program name contains invalid characters.',
        ];
    }

    /**
     * Get all errors
     * 
     * @return array
     */
    public function getImportErrors()
    {
        return $this->importErrors;
    }
    
    /**
     * @return array
     */
    public function getErrors()
    {
        return $this->errors;
    }
}
