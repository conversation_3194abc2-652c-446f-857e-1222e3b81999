<?php

namespace App\Imports;

use App\Models\StudentCgpa;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class StudentCgpaImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        // Check if student with this index number already exists
        $existingStudent = StudentCgpa::where('index_number', $row['index_number'])->first();
        
        if ($existingStudent) {
            // Update existing student
            $existingStudent->update([
                'name' => $row['name'],
                'gpa' => $row['gpa'],
                'cgpa' => $row['cgpa'],
                'totalpoint' => $row['total_point'],
                'totalcredit' => $row['total_credit'],
                'program' => $row['program'],
                'department' => $row['department'],
                'semester' => $row['semester'],
                'year_of_enrollment' => $row['year_of_enrollment'],
                'email' => $row['email'] ?? null,
            ]);
            
            return null; // Don't create a new model
        }

        return new StudentCgpa([
            'index_number' => $row['index_number'],
            'name' => $row['name'],
            'gpa' => $row['gpa'],
            'cgpa' => $row['cgpa'],
            'totalpoint' => $row['total_point'],
            'totalcredit' => $row['total_credit'],
            'program' => $row['program'],
            'department' => $row['department'],
            'semester' => $row['semester'],
            'year_of_enrollment' => $row['year_of_enrollment'],
            'email' => $row['email'] ?? null,
        ]);
    }

    public function rules(): array
    {
        return [
            'index_number' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'gpa' => 'required|numeric|min:0|max:4',
            'cgpa' => 'required|numeric|min:0|max:4',
            'total_point' => 'required|numeric|min:0',
            'total_credit' => 'required|numeric|min:0',
            'program' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'semester' => 'required|string|max:255',
            'year_of_enrollment' => 'required|integer|min:1900|max:' . (date('Y') + 10),
            'email' => 'nullable|email|max:255',
        ];
    }

    public function customValidationMessages()
    {
        return [
            'index_number.required' => 'Index number is required.',
            'name.required' => 'Student name is required.',
            'gpa.required' => 'GPA is required.',
            'gpa.numeric' => 'GPA must be a number.',
            'gpa.min' => 'GPA must be at least 0.',
            'gpa.max' => 'GPA cannot exceed 4.',
            'cgpa.required' => 'CGPA is required.',
            'cgpa.numeric' => 'CGPA must be a number.',
            'cgpa.min' => 'CGPA must be at least 0.',
            'cgpa.max' => 'CGPA cannot exceed 4.',
            'total_point.required' => 'Total point is required.',
            'total_credit.required' => 'Total credit is required.',
            'program.required' => 'Program is required.',
            'department.required' => 'Department is required.',
            'semester.required' => 'Semester is required.',
            'year_of_enrollment.required' => 'Year of enrollment is required.',
            'email.email' => 'Please enter a valid email address.',
        ];
    }

    public function headingRow(): int
    {
        return 1;
    }
}
