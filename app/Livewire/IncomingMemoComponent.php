<?php

namespace App\Livewire;

use App\Models\IncomingMemo;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class IncomingMemoComponent extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $fromDate = '';
    public $toDate = '';
    public $sender = '';

    // Modal properties
    public $showModal = false;
    public $editMode = false;
    public $memoId = null;

    // Form properties
    public $date_received = '';
    public $registry_no = '';
    public $from_whom_received = '';
    public $date_of_letter = '';
    public $letter_no = '';
    public $subject = '';
    public $remark = '';

    protected $rules = [
        'date_received' => 'required|date',
        'registry_no' => 'required|string|max:50',
        'from_whom_received' => 'required|string|max:255',
        'date_of_letter' => 'required|date',
        'letter_no' => 'required|string|max:50',
        'subject' => 'required|string',
        'remark' => 'nullable|string',
    ];

    protected $messages = [
        'date_received.required' => 'Date received is required.',
        'registry_no.required' => 'Registry number is required.',
        'from_whom_received.required' => 'Sender is required.',
        'date_of_letter.required' => 'Date of letter is required.',
        'letter_no.required' => 'Letter number is required.',
        'subject.required' => 'Subject is required.',
    ];

    public function mount()
    {
        $this->date_received = now()->format('Y-m-d');
        $this->date_of_letter = now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFromDate()
    {
        $this->resetPage();
    }

    public function updatingToDate()
    {
        $this->resetPage();
    }

    public function updatingSender()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->fromDate = '';
        $this->toDate = '';
        $this->sender = '';
        $this->resetPage();
    }

    public function openModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->showModal = true;

        // Debug: Log that the method was called
        Log::info('IncomingMemo openModal called, showModal is now: ' . ($this->showModal ? 'true' : 'false'));
    }

    public function editMemo($id)
    {
        $memo = IncomingMemo::findOrFail($id);
        
        $this->memoId = $memo->id;
        $this->date_received = $memo->date_received->format('Y-m-d');
        $this->registry_no = $memo->registry_no;
        $this->from_whom_received = $memo->from_whom_received;
        $this->date_of_letter = $memo->date_of_letter->format('Y-m-d');
        $this->letter_no = $memo->letter_no;
        $this->subject = $memo->subject;
        $this->remark = $memo->remark;
        
        $this->editMode = true;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->memoId = null;
        $this->date_received = now()->format('Y-m-d');
        $this->registry_no = '';
        $this->from_whom_received = '';
        $this->date_of_letter = now()->format('Y-m-d');
        $this->letter_no = '';
        $this->subject = '';
        $this->remark = '';
        $this->resetErrorBag();
    }

    public function save()
    {
        $this->validate();

        try {
            $data = [
                'date_received' => $this->date_received,
                'registry_no' => $this->registry_no,
                'from_whom_received' => $this->from_whom_received,
                'date_of_letter' => $this->date_of_letter,
                'letter_no' => $this->letter_no,
                'subject' => $this->subject,
                'remark' => $this->remark,
                'created_by' => Auth::id(),
            ];

            if ($this->editMode) {
                $memo = IncomingMemo::findOrFail($this->memoId);
                $memo->update($data);
                flash()->success('Incoming memo updated successfully!');
            } else {
                IncomingMemo::create($data);
                flash()->success('Incoming memo created successfully!');
            }

            $this->closeModal();
        } catch (\Exception $e) {
            flash()->error('An error occurred while saving the memo.');
        }
    }

    public $confirmingDeletion = false;
    public $memoToDelete = null;

    // Debug counter
    public $testCounter = 0;

    public function incrementCounter()
    {
        $this->testCounter++;
        Log::info('Counter incremented to: ' . $this->testCounter);
    }

    public function confirmDelete($id)
    {
        $this->memoToDelete = $id;
        $this->confirmingDeletion = true;
    }

    public function cancelDelete()
    {
        $this->confirmingDeletion = false;
        $this->memoToDelete = null;
    }

    public function deleteMemo()
    {
        try {
            if ($this->memoToDelete) {
                IncomingMemo::findOrFail($this->memoToDelete)->delete();
                flash()->success('Incoming memo deleted successfully!');
            }
        } catch (\Exception $e) {
            flash()->error('An error occurred while deleting the memo.');
        } finally {
            $this->cancelDelete();
        }
    }

    public function render()
    {
        $memos = IncomingMemo::with('creator')
            ->search($this->search)
            ->dateRange($this->fromDate, $this->toDate)
            ->fromSender($this->sender)
            ->latest('date_received')
            ->paginate(10);

        return view('livewire.incoming-memo-component', compact('memos'));
    }
}
