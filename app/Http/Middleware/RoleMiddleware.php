<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  mixed ...$roles
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Normalize roles for comparison
        $userRole = strtolower(trim($user->role ?? ''));
        $requiredRoles = array_map(fn($role) => strtolower(trim($role)), $roles);

        if (empty($requiredRoles) || in_array($userRole, $requiredRoles)) {
            return $next($request);
        }

        // If user doesn't have required role, redirect to appropriate dashboard
        $dashboardRoute = match($userRole) {
            'admin' => 'admin.dashboard',
            'secretary' => 'secretary.dashboard',
            default => 'login'
        };

        return redirect()->route($dashboardRoute)->with('error', 'You do not have permission to access this page.');

        return $next($request);
    }
}
