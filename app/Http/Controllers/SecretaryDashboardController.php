<?php

namespace App\Http\Controllers;


use App\Models\Document;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\Auth;

class SecretaryDashboardController extends Controller
{
    /**
     * Display secretary dashboard metrics.
     */
    public function index()
    {
        $user = Auth::user();


        $documentCount = Document::where('user_id', $user->id)->count();
        $logCount      = ActivityLog::where('user_id', $user->id)->count();

        return view('dashboard.secretary', compact(
            'documentCount',
            'logCount'
        ));
    }
}
