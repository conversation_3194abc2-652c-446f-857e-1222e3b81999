<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Mail\NewUserCredentials;
use App\Models\ActivityLog;
class UserController extends Controller
{
    /**
     * Display a listing of the users.
     */
    public function index()
    {
        Log::info('User index page requested.');
        $users = User::select('id', 'role', 'name', 'email', 'email_verified_at')
            ->orderBy('name')
            ->paginate(15);

        return view('users.index', compact('users'));
    }

        /**
     * Show create user form (not used – handled in modal on index)
     */
    public function create()
    {
        return redirect()->route('users.index');
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'role' => ['required', 'in:Admin,Accountant,Secretary,HOD'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email'],
        ]);

        try {
            Log::info('Attempting to create a new user', $validated);
            $plainPassword = Str::random(10);

            $user = User::create([
                'role'     => $validated['role'],
                'name'     => $validated['name'],
                'email'    => $validated['email'],
                'password' => Hash::make($plainPassword),
            ]);

            Mail::to($user->email)->send(new NewUserCredentials($user, $plainPassword));
            Log::info('New user credentials sent', ['user_id' => $user->id, 'email' => $user->email]);

            ActivityLog::create([
                'user_id'    => auth()->id(),
                'role'       => auth()->user()->role,
                'action'     => 'create',
                'description'=> 'Created user ' . $user->email,
                'ip_address' => $request->ip(),
            ]);

            flash()->success('User created successfully and credentials emailed.');
            return redirect()->route('users.index');
        } catch (\Exception $e) {
            Log::error('User creation failed', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('Failed to create user. Please try again.');
            return back()->withInput();
        }
    }

    /**
     * Show edit user form (not used – handled in modal)
     */
    public function edit(User $user)
    {
        return redirect()->route('users.index', ['edit' => $user->id]);
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'role'  => ['required', 'in:Admin,Accountant,Secretary,HOD'],
            'name'  => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', Rule::unique('users','email')->ignore($user->id)],
        ]);

        Log::info('Attempting to update user', ['user_id' => $user->id, 'data' => $validated]);
        $user->update($validated);

        ActivityLog::create([
            'user_id'    => auth()->id(),
            'role'       => auth()->user()->role,
            'action'     => 'update',
            'description'=> 'Updated user ' . $user->email,
            'ip_address' => $request->ip(),
        ]);

        Log::info('User updated successfully', ['user_id' => $user->id]);
        flash()->success('User updated successfully.');
        return redirect()->route('users.index');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(Request $request, User $user)
    {
        try {
            Log::info('Attempting to delete user', ['user_id' => $user->id, 'admin_id' => auth()->id()]);
            $user->delete();

            ActivityLog::create([
                'user_id'    => auth()->id(),
                'role'       => auth()->user()->role,
                'action'     => 'delete',
                'description'=> 'Deleted user ' . $user->email,
                'ip_address' => $request->ip(),
            ]);

            Log::info('User deleted successfully', ['user_id' => $user->id]);
            flash()->success('User deleted successfully.');
            return redirect()->route('users.index');
        } catch (\Exception $e) {
            Log::error('User deletion failed', ['user_id' => $user->id, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('Failed to delete user. Please try again.');
            return back();
        }
    }
}
