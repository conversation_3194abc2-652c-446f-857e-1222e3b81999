<?php

namespace App\Http\Controllers;

use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PromotionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Promotion::query();

        // Filtering
        if ($request->filled('position')) {
            $query->where('position', $request->input('position'));
        }

        if ($request->filled('year')) {
            $query->whereYear('year_of_promotion', $request->input('year'));
        }

        if ($search = $request->input('q')) {
            $query->where('name', 'like', "%{$search}%");
        }

        $promotions = $query->latest()->paginate(10);

        return view('promotions.index', compact('promotions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('promotions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'position' => ['required', Rule::in(['Dean','Exam Officer','Program Coordinator','SAA','Faculty Officer','HOD'])],
            'year_of_promotion' => ['required', 'date'],
            'end_of_promotion' => ['required', 'date', 'after:year_of_promotion']
        ]);

        $promotion = Promotion::create($validated);

        // Log activity
        \App\Models\ActivityLog::create([
            'user_id' => $request->user()->id ?? null,
            'role' => $request->user()->role ?? 'guest',
            'action' => 'create',
            'description' => 'Created promotion for ' . $promotion->name,
            'ip_address' => $request->ip(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Promotion created successfully.',
                'promotion' => $promotion
            ]);
        }

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Promotion $promotion)
    {
        // This view might still be useful for individual promotion details or API.
        // If not, it can be removed later.
        return view('promotions.show', compact('promotion'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Promotion $promotion)
    {
        // This view will likely not be needed if editing is done via modal.
        // Can be removed in a later step.
        return view('promotions.edit', compact('promotion'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Promotion $promotion)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'position' => ['required', Rule::in(['Dean','Exam Officer','Program Coordinator','SAA','Faculty Officer','HOD'])],
            'year_of_promotion' => ['required', 'date'],
            'end_of_promotion' => ['required', 'date', 'after:year_of_promotion']
        ]);

        $promotion->update($validated);

        // Log activity
        \App\Models\ActivityLog::create([
            'user_id' => $request->user()->id ?? null,
            'role' => $request->user()->role ?? 'guest',
            'action' => 'update',
            'description' => 'Updated promotion for ' . $promotion->name,
            'ip_address' => $request->ip(),
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Promotion updated successfully.',
                'promotion' => $promotion
            ]);
        }

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, Promotion $promotion)
    {
        $promotionName = $promotion->name; // Capture name before deletion
        $promotion->delete();

        // Log activity
        \App\Models\ActivityLog::create([
            'user_id' => auth()->id() ?? null,
            'role' => auth()->user()->role ?? 'guest',
            'action' => 'delete',
            'description' => 'Deleted promotion: ' . $promotionName,
            'ip_address' => request()->ip(),
        ]);

        if ($request->expectsJson()) {
            return response()->json(['message' => 'Promotion deleted successfully.']);
        }

        return redirect()->route('promotions.index')
            ->with('success', 'Promotion deleted successfully');
    }
}

