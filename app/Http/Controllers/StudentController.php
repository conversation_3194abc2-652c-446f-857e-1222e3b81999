<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Imports\StudentsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index(Request $request)
    {
        $query = Student::query();

        // Search across fields
        if ($search = $request->input('q')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%")
                  ->orWhere('program', 'like', "%{$search}%")
                  ->orWhere('index_number', 'like', "%{$search}%");
            });
        }

        // Filters
        if ($dept = $request->input('department')) {
            $query->where('department', $dept);
        }
        if ($program = $request->input('program')) {
            $query->where('program', $program);
        }
        if ($year = $request->input('year')) {
            $query->where('year_of_enrollment', $year);
        }

        $students = $query->latest()->paginate(10);
        
        // Get unique departments for the filter dropdown
        $departments = Student::select('department')
            ->distinct()
            ->orderBy('department')
            ->pluck('department');

        return view('students.index', compact('students', 'departments'));
    }

    public function create()
    {
        return view('students.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'       => ['required', 'string', 'max:255', 'regex:/^[A-Za-z.\s]+$/'],
            'department'         => ['required', 'string', 'max:255', 'regex:/^[A-Za-z\s-]+$/'],
            'year_of_enrollment' => [
                'required', 
                'digits:4', 
                'integer',
                'min:1900',
                'max:' . date('Y')
            ],
            'program'            => ['required', 'string', 'max:255', 'regex:/^[A-Za-z\s-]+$/'],
            'index_number'       => [
                'required', 
                'string', 
                'digits:10',
                'unique:fbne_students,index_number'
            ],
        ], [
            'name.regex' => 'The name field may only contain letters, spaces, and dots.',
            'year_of_enrollment.digits' => 'The year of enrollment must be exactly 4 digits.',
            'year_of_enrollment.min' => 'The year of enrollment must be 1900 or later.',
            'year_of_enrollment.max' => 'The year of enrollment cannot be in the future.',
            'index_number.digits' => 'The index number must be exactly 10 digits.',
            'index_number.unique' => 'A student with this index number already exists.',
            'department.regex' => 'The department name contains invalid characters.',
            'program.regex' => 'The program name contains invalid characters.',
        ]);

        $student = Student::create($validated);
        $successMessage = 'Student ' . $validated['name'] . ' has been successfully added.';

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage,
                'student' => $student
            ]);
        }

        return redirect()->route('students.index')
            ->with('success', $successMessage);
    }

    public function edit(Student $student)
    {
        return view('students.edit', compact('student'));
    }

    public function update(Request $request, Student $student)
    {
        $validated = $request->validate([
            'name'       => ['required', 'string', 'max:255', 'regex:/^[A-Za-z.\s]+$/'],
            'department'         => ['required', 'string', 'max:255', 'regex:/^[A-Za-z\s-]+$/'],
            'year_of_enrollment' => [
                'required', 
                'digits:4', 
                'integer',
                'min:1900',
                'max:' . date('Y')
            ],
            'program'            => ['required', 'string', 'max:255', 'regex:/^[A-Za-z\s-]+$/'],
            'index_number'       => [
                'required', 
                'string', 
                'digits:10',
                \Illuminate\Validation\Rule::unique('fbne_students', 'index_number')->ignore($student->id)
            ],
        ], [
            'name.regex' => 'The name field may only contain letters, spaces, and dots.',
            'year_of_enrollment.digits' => 'The year of enrollment must be exactly 4 digits.',
            'year_of_enrollment.min' => 'The year of enrollment must be 1900 or later.',
            'year_of_enrollment.max' => 'The year of enrollment cannot be in the future.',
            'index_number.digits' => 'The index number must be exactly 10 digits.',
            'index_number.unique' => 'A student with this index number already exists.',
            'department.regex' => 'The department name contains invalid characters.',
            'program.regex' => 'The program name contains invalid characters.',
        ]);

        $student->update($validated);
        $successMessage = 'Student ' . $validated['name'] . ' has been successfully updated.';

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $successMessage,
                'student' => $student
            ]);
        }

        return redirect()->route('students.index')
            ->with('success', $successMessage);
    }

    public function destroy(Student $student)
    {
        $studentName = $student->name;
        $student->delete();
        
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Student deleted successfully!'
            ]);
        }
        
        return redirect()->route('students.index')
            ->with('success', 'Student "' . $studentName . '" deleted successfully!');
    }

    /**
     * Export students as CSV (respects current filters).
     */
    /**
     * Import students from Excel file
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240' // 10MB max
        ]);

        try {
            $import = new StudentsImport();
            Excel::import($import, $request->file('file'));
            
            $importErrors = $import->getImportErrors();
            $failures = $import->failures();
            
            $importedCount = $import->getRowCount() - count($importErrors) - count($failures);
            $errorCount = count($importErrors) + count($failures);
            
            $message = "Successfully imported {$importedCount} students.";
            
            if ($errorCount > 0) {
                $message .= " {$errorCount} records had errors.";
                
                // Add detailed error messages
                $failureMessages = [];
                if (!empty($failures)) {
                    foreach ($failures as $failure) {
                        $row = method_exists($failure, 'row') ? $failure->row() : '?';
                        $error = 'Validation error';
                        
                        if (method_exists($failure, 'errors')) {
                            $errors = $failure->errors();
                            $error = is_array($errors) ? ($errors[0] ?? 'Validation error') : 'Validation error';
                        }
                        
                        $failureMessages[] = 'Row ' . $row . ': ' . $error;
                    }
                }
                
                $errors = array_merge($importErrors, $failureMessages);
                
                return redirect()->back()
                    ->with('import_errors', $errors)
                    ->with('success', $message);
            }
            
            return redirect()->back()->with('success', $message);
            
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error importing file: ' . $e->getMessage());
        }
    }

    /**
     * Export students to CSV
     */
    public function export(Request $request)
    {
        $filename = 'students_' . now()->format('Ymd_His') . '.csv';

        $query = Student::query();
        if ($search = $request->input('q')) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%")
                  ->orWhere('program', 'like', "%{$search}%")
                  ->orWhere('index_number', 'like', "%{$search}%");
            });
        }
        if ($dept = $request->input('department')) {
            $query->where('department', $dept);
        }
        if ($program = $request->input('program')) {
            $query->where('program', $program);
        }
        if ($year = $request->input('year')) {
            $query->where('year_of_enrollment', $year);
        }

        $students = $query->orderBy('student_name')->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($students) {
            $file = fopen('php://output', 'w');
            // Header row
            fputcsv($file, ['Name', 'Department', 'Program', 'Year', 'Index Number']);
            foreach ($students as $s) {
                fputcsv($file, [$s->name, $s->department, $s->program, $s->year_of_enrollment, $s->index_number]);
            }
            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }
}