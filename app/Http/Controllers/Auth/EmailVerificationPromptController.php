<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class EmailVerificationPromptController extends Controller
{
    /**
     * Display the email verification prompt.
     */
    public function __invoke(Request $request): RedirectResponse|View
    {
        if ($request->user()->hasVerifiedEmail()) {
            Log::info('User has verified email, redirecting to dashboard.', ['user_id' => $request->user()->id]);
            return redirect()->intended(route('dashboard', absolute: false));
        } else {
            Log::info('Showing email verification prompt.', ['user_id' => $request->user()->id]);
            return view('auth.verify-email');
        }
    }
}
