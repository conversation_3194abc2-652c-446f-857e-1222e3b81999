<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\View\View;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): View
    {
        return view('auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        try {
            $request->validate([
                'email' => ['required', 'email'],
            ]);

            if (empty($request->email)) {
                flash()->error('Email is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['email' => ['Email is required.']],
                    'email'
                );
            }

            // We will send the password reset link to this user. Once we have attempted
            // to send the link, we will examine the response then see the message we
            // need to show to the user. Finally, we'll send out a proper response.
            $status = Password::sendResetLink(
                $request->only('email')
            );

            if ($status == Password::RESET_LINK_SENT) {
                flash()->success('Password reset link sent successfully. Please check your email.', [
                    'title' => 'Success'
                ]);
                return back()->with('status', __($status));
            } else {
                flash()->error('Failed to send password reset link. Please try again.', [
                    'title' => 'Error'
                ]);
                return back()->withInput($request->only('email'))
                    ->withErrors(['email' => __($status)]);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Password reset link error: ' . $e->getMessage());
            flash()->error('An error occurred while sending password reset link. Please try again.', [
                'title' => 'Error'
            ]);
            return back()->withInput($request->only('email'))
                ->withErrors(['email' => 'An error occurred. Please try again.']);
        }
    }
}
