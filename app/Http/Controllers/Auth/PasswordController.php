<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Password;

class PasswordController extends Controller
{
    /**
     * Update the user's password.
     */
    public function update(Request $request): RedirectResponse
    {
        Log::info('Password update attempt.', ['user_id' => $request->user()->id]);
        try {
            $validated = $request->validateWithBag('updatePassword', [
                'current_password' => ['required', 'current_password'],
                'password' => ['required', Password::defaults(), 'confirmed'],
            ]);

            $request->user()->update([
                'password' => Hash::make($validated['password']),
            ]);

            Log::info('Password updated successfully.', ['user_id' => $request->user()->id]);
            flash()->success('Password updated successfully.');
            return back()->with('status', 'password-updated');
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Password update validation failed.', ['user_id' => $request->user()->id, 'errors' => $e->errors()]);
            throw $e;
        } catch (\Exception $e) {
            Log::error('Password update error.', ['user_id' => $request->user()->id, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('An unexpected error occurred. Please try again.');
            return back();
        }
    }
}
