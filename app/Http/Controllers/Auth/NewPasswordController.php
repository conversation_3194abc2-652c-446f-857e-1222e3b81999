<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class NewPasswordController extends Controller
{
    /**
     * Display the password reset view.
     */
    public function create(Request $request): View
    {
        Log::info('Password reset page requested.', ['email' => $request->email]);
        return view('auth.reset-password', ['request' => $request]);
    }

    /**
     * Handle an incoming new password request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        Log::info('Attempting to reset password.', ['email' => $request->email]);
        try {
            $request->validate([
                'token' => ['required'],
                'email' => ['required', 'email'],
                'password' => ['required', 'confirmed', Rules\Password::defaults()],
            ]);

            $status = Password::reset(
                $request->only('email', 'password', 'password_confirmation', 'token'),
                function (User $user) use ($request) {
                    $user->forceFill([
                        'password' => Hash::make($request->password),
                        'remember_token' => Str::random(60),
                    ])->save();

                    Log::info('User password updated successfully after reset.', ['user_id' => $user->id]);
                    event(new PasswordReset($user));
                }
            );

            if ($status == Password::PASSWORD_RESET) {
                Log::info('Password reset successful.', ['email' => $request->email]);
                flash()->success(__($status));
                return redirect()->route('login');
            }

            Log::warning('Password reset failed.', ['email' => $request->email, 'status' => $status]);
            flash()->error(__($status));
            return back()->withInput($request->only('email'))
                ->withErrors(['email' => __($status)]);
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed during password reset.', ['errors' => $e->errors()]);
            // The default handler will flash errors to the session and redirect back.
            throw $e;
        } catch (\Exception $e) {
            Log::error('An unexpected error occurred during password reset.', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('An unexpected error occurred. Please try again.');
            return back()->withInput($request->only('email'));
        }
    }
}
