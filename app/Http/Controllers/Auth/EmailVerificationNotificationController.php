<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EmailVerificationNotificationController extends Controller
{
    /**
     * Send a new email verification notification.
     */
    public function store(Request $request): RedirectResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            Log::info('User already verified, redirecting to dashboard.', ['user_id' => $request->user()->id]);
            return redirect()->intended(route('dashboard', absolute: false));
        }

        $request->user()->sendEmailVerificationNotification();
        Log::info('Email verification link sent.', ['user_id' => $request->user()->id]);

        flash()->success('A new verification link has been sent to your email address.');

        return back();
    }
}
