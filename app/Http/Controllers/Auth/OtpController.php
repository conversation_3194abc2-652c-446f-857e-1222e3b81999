<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use App\Mail\LoginOtp;
use Illuminate\Support\Facades\Mail;
use App\Models\ActivityLog;

class OtpController extends Controller
{
    /** Show OTP input form */
    public function create(Request $request)
    {
        if (! $request->session()->has('otp:user')) {
            Log::warning('OTP page accessed without a pending user session.', ['ip_address' => $request->ip()]);
            return redirect()->route('login');
        }

        Log::info('OTP input form requested.', ['user_id_pending' => $request->session()->get('otp:user')]);
        return view('auth.otp');
    }

    /** Process OTP verification */
    public function store(Request $request)
    {
        $request->validate([
            'otp' => ['required', 'digits:6'],
        ]);

        if (! $request->session()->has('otp:user')) {
            Log::warning('OTP submitted without a pending user session.', ['ip_address' => $request->ip()]);
            return redirect()->route('login');
        }

        $userId = $request->session()->get('otp:user');
        Log::info('Attempting OTP verification.', ['user_id' => $userId]);
        $throttleKey = 'otp|'.$userId.'|'.$request->ip();

        if (RateLimiter::tooManyAttempts($throttleKey, 5)) {
            Log::warning('OTP verification failed due to too many attempts.', ['user_id' => $userId, 'ip_address' => $request->ip()]);
            throw ValidationException::withMessages([
                'otp' => 'Too many attempts. Please try again later.',
            ]);
        }

        $cachedHash = Cache::get('otp_code_'.$userId);

        if (! $cachedHash || ! Hash::check($request->otp, $cachedHash)) {
            RateLimiter::hit($throttleKey, 60);
            Log::warning('Invalid or expired OTP entered.', ['user_id' => $userId, 'ip_address' => $request->ip()]);
            throw ValidationException::withMessages([
                'otp' => 'Invalid or expired OTP.',
            ]);
        }

        // Get the user
        $user = User::findOrFail($userId);
        
        // Prepare update data
        $isFirstLogin = is_null($user->last_login_at);
        $now = now();
        
        // Update user record with fresh data
        $user->last_login_at = $now;
        
        // For first-time login, mark as verified
        if ($isFirstLogin) {
            $user->email_verified_at = $now;
            // Note: 'status' column doesn't exist in the users table
        }
        
        // Save the user model
        $user->save();
        
        // Log the user in
        $remember = $request->session()->get('otp:remember', false);
        Auth::login($user, $remember);
        
        // Log the successful login
        Log::info('User logged in successfully via OTP.', [
            'user_id' => $user->id,
            'is_first_login' => $isFirstLogin,
            'last_login_at' => $user->last_login_at,
            'email_verified_at' => $user->email_verified_at
        ]);

        // Record activity log
        $isFirstLogin = is_null($user->last_login_at);
        $description = $isFirstLogin 
            ? 'User logged in for the first time and verified' 
            : 'User logged in with OTP';
            
        \App\Models\ActivityLog::create([
            'user_id'     => $user->id,
            'role'        => $user->role,
            'action'      => 'login',
            'description' => $description,
            'ip_address'  => $request->ip(),
        ]);

        // Cleanup
        $request->session()->regenerate();
        $request->session()->forget(['otp:user', 'otp:remember']);
        Cache::forget('otp_code_'.$userId);
        RateLimiter::clear($throttleKey);

        flash()->success($isFirstLogin 
            ? 'Your account has been verified and you are now logged in.' 
            : 'You have been logged in successfully.');

        // Redirect based on role
        return redirect()->intended(
            $user->role === 'Admin' 
                ? route('admin.dashboard') 
                : route('secretary.dashboard')
        );
    }

    /** Helper to (re)send OTP */
    public function resend(Request $request)
    {
        if (! $request->session()->has('otp:user')) {
            Log::warning('OTP resend requested without a pending user session.', ['ip_address' => $request->ip()]);
            return redirect()->route('login');
        }

        $userId = $request->session()->get('otp:user');
        Log::info('Resending OTP.', ['user_id' => $userId]);
        $user = User::findOrFail($userId);

        $otp = random_int(100000, 999999);
        Cache::put('otp_code_'.$userId, Hash::make($otp), now()->addMinutes(5));
        Mail::to($user->email)->send(new LoginOtp($user, $otp));

        flash()->success('A new OTP has been sent to your email.');
        return back();
    }
}