<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use App\Mail\LoginOtp;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        Log::info('Login page requested.');
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $this->ensureCredentialsValid($request);

        $user = \App\Models\User::where('email', $request->email)->first();

        if ($user) {
            // Check if this is the user's first login (last_login_at is null)
            if (is_null($user->last_login_at)) {
                // Generate and send OTP for first-time login
                $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
                $expiresAt = now()->addMinutes(15);
                
                // Store OTP in cache
                Cache::put("otp:{$user->id}", [
                    'code' => $otp,
                    'expires_at' => $expiresAt,
                ], $expiresAt);

                // Send OTP via email
                try {
                    Mail::to($user->email)->send(new \App\Mail\LoginOtp($user, $otp));
                } catch (\Exception $e) {
                    Log::error('Failed to send OTP email', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ]);
                    
                    throw ValidationException::withMessages([
                        'email' => 'Failed to send OTP. Please try again later.'
                    ]);
                }

                // Store user ID in session for OTP verification
                $request->session()->put('otp:user', $user->id);
                $request->session()->put('otp:remember', $request->boolean('remember'));

                Log::info('OTP sent for first-time login', ['user_id' => $user->id]);
                
                return redirect()->route('otp.create');
            }
            
            // For returning users, update last_login_at and log them in
            $user->update(['last_login_at' => now()]);
            Auth::login($user, $request->boolean('remember'));

            // Record activity
            \App\Models\ActivityLog::create([
                'user_id'     => $user->id,
                'role'        => $user->role,
                'action'      => 'login',
                'description' => 'User logged in',
                'ip_address'  => $request->ip(),
            ]);

            // Regenerate session to prevent fixation
            $request->session()->regenerate();

            Log::info('User logged in without OTP', [
                'user_id' => $user->id,
                'role' => $user->role
            ]);
            
            flash()->success('You have been logged in successfully.');

            // Normalize role and redirect to appropriate dashboard
            $role = strtolower(trim($user->role));
            $dashboardRoute = match($role) {
                'admin' => 'admin.dashboard',
                'secretary' => 'secretary.dashboard',
                default => 'login'
            };

            return redirect()->intended(route($dashboardRoute));
        }

        // First-time login – proceed with OTP flow
        try {
            $otp = random_int(100000, 999999);
            Cache::put('otp_code_'.$user->id, Hash::make($otp), now()->addMinutes(5));
            Mail::to($user->email)->send(new LoginOtp($user, $otp));

            // store pending user id in session
            $request->session()->put('otp:user', $user->id);

            Log::info('OTP sent for first-time login', ['user_id' => $user->id]);
            flash()->success('An OTP has been sent to your email. Please verify to complete login.');
            return redirect()->route('otp.create');
        } catch (\Symfony\Component\Mailer\Exception\TransportException $e) {
            Log::error('SMTP transport error during login', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('Failed to send OTP. Please check your network connection and try again.');
            return back()->withInput();
        } catch (\Exception $e) {
            Log::error('Generic authentication error', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('An unexpected error occurred during login. Please try again.');
            return back()->withInput();
        }
    }

    /**
     * Validate credentials without logging the user in.
     */
    private function ensureCredentialsValid(LoginRequest $request): void
    {
        // Rate limiting logic borrowed from LoginRequest::authenticate
        $request->ensureIsNotRateLimited();

        if (!Auth::validate($request->only('email', 'password'))) {
            RateLimiter::hit($request->throttleKey());
            Log::warning('Invalid login credentials', ['email' => $request->email, 'ip_address' => $request->ip()]);
            flash()->error('Invalid email or password. Please try again.');
            
            throw \Illuminate\Validation\ValidationException::withMessages([
                'email' => trans('auth.failed'),
            ]);
        }

        // keep rate limiter cleared
        RateLimiter::clear($request->throttleKey());
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $user = $request->user();
        Log::info('User logging out', ['user_id' => $user->id]);
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // flash()->success removed to avoid showing notification on logout

        return redirect('/');
    }
}
