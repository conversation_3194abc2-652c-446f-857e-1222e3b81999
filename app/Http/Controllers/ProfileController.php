<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): View
    {
        Log::info('Profile edit page requested', ['user_id' => $request->user()->id]);
        return view('profile.edit', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        Log::info('Profile update attempt', ['user_id' => $request->user()->id, 'data' => $request->validated()]);
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            Log::info('User email changed, marking for re-verification', ['user_id' => $request->user()->id]);
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        Log::info('Profile updated successfully', ['user_id' => $request->user()->id]);
        flash()->success('Profile updated successfully.');
        return Redirect::route('profile.edit');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Log::info('Account deletion attempt', ['user_id' => $request->user()->id]);
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        Log::info('User account deleted successfully', ['user_id' => $user->id]);
        flash()->success('Your account has been deleted successfully.');
        return Redirect::to('/');
    }
}
