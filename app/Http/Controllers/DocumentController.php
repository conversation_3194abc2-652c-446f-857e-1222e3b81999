<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpFoundation\Response;

class DocumentController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        Log::info('Document index requested', ['user_id' => $user->id, 'is_admin' => $user->is_admin, 'request' => $request->all()]);
        
        // Start building the query
        $query = Document::with('user');

        // If user is not an admin, only show their own documents
        if ($user->role !== 'admin') {
            $query->where('user_id', $user->id);
        }

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            Log::info('Searching documents', ['search_term' => $search, 'user_id' => $user->id]);
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('category', 'LIKE', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply category filter if provided
        if ($request->has('category') && !empty($request->category)) {
            Log::info('Filtering documents by category', ['category' => $request->category, 'user_id' => $user->id]);
            $query->where('category', $request->category);
        }

        // Order and paginate results
        $documents = $query->orderBy('created_at', 'desc')
                          ->paginate(15)
                          ->appends($request->query());
                          
        $categories = Document::getCategories();

        // Handle AJAX requests (for search/filter updates)
        if ($request->ajax()) {
            $html = view('documents.partials.documents-table', compact('documents', 'categories'))->render();
            return response()->json([
                'html' => $html,
                'count' => $documents->total(),
                'is_admin' => $user->is_admin
            ]);
        }
    
        Log::info('Returning document index view', ['document_count' => $documents->total()]);
        return view('documents.index', compact('documents', 'categories'));
    }

    public function store(Request $request)
    {
        Log::info('Document store request initiated', ['user_id' => Auth::id()]);
        try {
            $validator = Validator::make($request->all(), [
                'files.*' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx|max:10240',
                'category' => 'required|string|in:meetings,memos,reports,student-affairs,staff-affairs'
            ]);

            if ($validator->fails()) {
                Log::warning('Document validation failed', ['errors' => $validator->errors()->all()]);
                flash()->error('Validation failed. Please check the form.');
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $uploadedFiles = [];
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $randomName = Str::random(40) . '.' . $extension;
                    $filePath = 'documents/' . $randomName;

                    Log::info('Storing file', ['original_name' => $originalName, 'path' => $filePath]);
                    // Save into private storage app/private/documents/<randomName>
                    $file->storeAs('documents', $randomName, 'local');

                    $document = Document::create([
                        'name' => $originalName,
                        'file_path' => $filePath,
                        'file_type' => strtolower($extension),
                        'category' => $request->category,
                        'user_id' => Auth::id(),
                        'download_count' => 0
                    ]);
                    
                    // Log document upload activity
                    ActivityLog::create([
                        'user_id' => Auth::id(),
                        'role' => Auth::user()->role,
                        'action' => 'upload_document',
                        'description' => 'Uploaded document: ' . $originalName . ' (ID: ' . $document->id . ')' . 
                                        ' | Category: ' . $request->category . 
                                        ' | Type: ' . strtoupper($extension) . 
                                        ' | Size: ' . number_format($file->getSize() / 1024, 2) . ' KB',
                        'ip_address' => $request->ip()
                    ]);
                    
                    $uploadedFiles[] = $document;
                }
            }

            $message = count($uploadedFiles) . ' file(s) uploaded successfully.';
            Log::info('Documents uploaded successfully', ['count' => count($uploadedFiles), 'user_id' => Auth::id()]);
            
            // Store flash message in the session
            session()->flash('success', $message);
            
            // For AJAX requests, include the flash message in the response
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'files' => $uploadedFiles,
                    'flash' => [
                        'type' => 'success',
                        'message' => $message
                    ]
                ]);
            }
            
            // For non-AJAX requests, redirect back with success message
            return redirect()->back()->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Document upload error', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('Failed to upload files. Please try again.');
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload files. Please try again.'
            ], 500);
        }
    }

    public function download(Request $request, Document $document): Response
    {
        Log::info('Document download request', ['document_id' => $document->id, 'user_id' => Auth::id()]);
        // Determine correct file path (handle legacy uploads stored under app/private/local)
        $filePath = storage_path('app/private/' . $document->file_path);
        if (!file_exists($filePath)) {
            // fallback for older uploads mistakenly saved in app/private/local/...
            $altPath = storage_path('app/private/local/' . $document->file_path);
            if (file_exists($altPath)) {
                $filePath = $altPath;
            }
        }
        if (!file_exists($filePath)) {
            Log::error('Download failed: File not found', ['document_id' => $document->id, 'path' => $filePath]);
            flash()->error('File not found and cannot be downloaded.');
            abort(404, 'File not found');
        }

        $document->incrementDownloadCount();
        
        // Log document download activity
        ActivityLog::create([
            'user_id' => Auth::id(),
            'role' => Auth::user()->role,
            'action' => 'download_document',
            'description' => 'Downloaded document: ' . $document->name . ' (ID: ' . $document->id . ')' . 
                            ' | Category: ' . $document->category . 
                            ' | Type: ' . strtoupper($document->file_type) . 
                            ' | Total Downloads: ' . ($document->download_count + 1),
            'ip_address' => $request->ip()
        ]);
        
        Log::info('Document download successful', ['document_id' => $document->id, 'user_id' => Auth::id()]);
        
        // Always show flash message for downloads
        flash()->success('Document is downloading: ' . $document->name);
        
        return response()->download($filePath, $document->name);
    }

    public function preview(Request $request, Document $document)
    {
        Log::info('Document preview request', ['document_id' => $document->id, 'user_id' => Auth::id(), 'ip' => $request->ip()]);
        // Determine correct file path (handle legacy uploads)
        $filePath = storage_path('app/private/' . $document->file_path);
        if (!file_exists($filePath)) {
            $altPath = storage_path('app/private/local/' . $document->file_path);
            if (file_exists($altPath)) {
                $filePath = $altPath;
            }
        }
        if (!file_exists($filePath)) {
            Log::error('Preview failed: File not found', ['document_id' => $document->id, 'path' => $filePath]);
            flash()->error('File not found and cannot be previewed.');
            abort(404, 'File not found');
        }

        if ($document->file_type === 'pdf') {
            // Log document preview activity
            ActivityLog::create([
                'user_id' => Auth::id(),
                'role' => Auth::user()->role,
                'action' => 'preview_document',
                'description' => 'Previewed PDF document: ' . $document->name . ' (ID: ' . $document->id . ')' . 
                                ' | Category: ' . $document->category,
                'ip_address' => $request->ip()
            ]);
            
            Log::info('Returning PDF preview inline', ['document_id' => $document->id]);
            return response()->file($filePath, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $document->name . '"'
            ]);
        }

        Log::info('File type not previewable, redirecting to download', ['document_id' => $document->id, 'file_type' => $document->file_type]);
        flash()->info('This file type cannot be previewed. Starting download instead.');
        return $this->download($document);
    }

    public function destroy(Request $request, Document $document)
    {
        Log::info('Document destroy request', ['document_id' => $document->id, 'user_id' => Auth::id()]);
        try {
            if ($document->user_id !== Auth::id() && Auth::user()->role !== 'admin') {
                Log::warning('Unauthorized document deletion attempt', ['document_id' => $document->id, 'user_id' => Auth::id()]);
                flash()->error('You are not authorized to delete this document.');
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to delete this document'
                ], 403);
            }

            // Log document deletion activity
            ActivityLog::create([
                'user_id' => Auth::id(),
                'role' => Auth::user()->role,
                'action' => 'delete_document',
                'description' => 'Deleted document: ' . $document->name . ' (ID: ' . $document->id . ')' . 
                                ' | Category: ' . $document->category . 
                                ' | Type: ' . strtoupper($document->file_type),
                'ip_address' => $request->ip()
            ]);
            
            if (Storage::disk('local')->exists($document->file_path)) {
                Log::info('Deleting document from storage', ['path' => $document->file_path]);
                Storage::disk('local')->delete($document->file_path);
            }

            $document->delete();

            Log::info('Document deleted successfully', ['document_id' => $document->id]);
            
            // Always show flash message for delete action
            flash()->success('Document deleted successfully: ' . $document->name);

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully: ' . $document->name
            ]);
        } catch (\Exception $e) {
            Log::error('Document deletion error', ['document_id' => $document->id, 'message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            flash()->error('Failed to delete document. Please try again.');
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document. Please try again.'
            ], 500);
        }
    }

    public function bulkDownload(Request $request) {
        Log::info('Bulk download request', ['user_id' => Auth::id(), 'document_ids' => $request->input('document_ids')]);
        $documentIds = $request->input('document_ids', []);
        
        if (empty($documentIds)) {
            Log::warning('Bulk download request with no documents selected');
            flash()->warning('No documents were selected for download.');
            return response()->json(['success' => false, 'message' => 'No documents selected'], 400);
        }

        $documents = Document::whereIn('id', $documentIds)->get();
        
        if ($documents->isEmpty()) {
            Log::warning('Bulk download request with no valid documents found', ['requested_ids' => $documentIds]);
            flash()->error('None of the selected documents could be found.');
            return response()->json(['success' => false, 'message' => 'No valid documents found'], 404);
        }

        $zip = new \ZipArchive();
        $zipFileName = 'documents_' . date('Y-m-d_H-i-s') . '.zip';
        $zipPath = storage_path('app/temp/' . $zipFileName);

        if (!file_exists(storage_path('app/temp'))) {
            mkdir(storage_path('app/temp'), 0755, true);
        }

        if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
            foreach ($documents as $document) {
                $filePath = storage_path('app/private/' . $document->file_path);
                if (file_exists($filePath)) {
                    $zip->addFile($filePath, $document->name);
                    $document->incrementDownloadCount();
                } else {
                    Log::warning('File not found during bulk download', ['document_id' => $document->id, 'path' => $filePath]);
                }
            }
            $zip->close();
            
            Log::info('Bulk download zip created successfully', ['zip_path' => $zipPath, 'file_count' => $documents->count()]);
            flash()->success('Your bulk download is starting.');
            return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
        }

        Log::error('Failed to create zip file for bulk download', ['zip_path' => $zipPath]);
        flash()->error('Could not create the zip file for bulk download.');
        return response()->json(['success' => false, 'message' => 'Failed to create zip file'], 500);
    }

    public function getStats() {
        Log::info('Statistics requested', ['user_id' => Auth::id()]);
        $stats = [
            'total_documents' => Document::count(),
            'total_downloads' => Document::sum('download_count'),
            'documents_by_category' => Document::selectRaw('category, COUNT(*) as count')
                ->groupBy('category')
                ->get()
                ->pluck('count', 'category'),
            'recent_uploads' => Document::with('user')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
        ];
        Log::info('Returning API stats', ['stats' => $stats]);
        return response()->json($stats);
    }
}
