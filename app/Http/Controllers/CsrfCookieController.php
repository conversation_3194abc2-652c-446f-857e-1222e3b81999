<?php

namespace Laravel\Sanctum\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Contracts\Encryption\Encrypter;
use Illuminate\Contracts\Routing\UrlGenerator;
use Symfony\Component\HttpFoundation\Cookie;

class CsrfCookieController
{
    /**
     * The URL generator instance.
     *
     * @var \Illuminate\Contracts\Routing\UrlGenerator
     */
    protected $url;

    /**
     * The encrypter instance.
     *
     * @var \Illuminate\Contracts\Encryption\Encrypter
     */
    protected $encrypter;

    /**
     * Create a new controller instance.
     *
     * @param  \Illuminate\Contracts\Routing\UrlGenerator  $url
     * @param  \Illuminate\Contracts\Encryption\Encrypter  $encrypter
     * @return void
     */
    public function __construct(UrlGenerator $url, Encrypter $encrypter)
    {
        $this->url = $url;
        $this->encrypter = $encrypter;
    }

    /**
     * Return an empty response that sets the XSRF-TOKEN cookie.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        $config = config('sanctum');

        $cookie = new Cookie(
            'XSRF-TOKEN',
            $request->session()->token(), // The CSRF token
            $this->getCookieExpirationDate(),
            $config['path'],
            $config['domain'],
            $config['secure'],
            false, // httpOnly
            false, // raw
            $config['same_site'] ?? null
        );

        return response('OK', 200)->withCookie($cookie);
    }

    /**
     * Get the cookie expiration date.
     *
     * @return int
     */
    protected function getCookieExpirationDate()
    {
        $config = config('session');

        return $config['expire_on_close'] ? 0 : time() + ($config['lifetime'] * 60);
    }
}
