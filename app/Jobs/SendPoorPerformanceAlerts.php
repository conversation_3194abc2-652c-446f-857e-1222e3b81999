<?php

namespace App\Jobs;

use App\Models\StudentCgpa;
use App\Mail\PoorPerformanceAlert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendPoorPerformanceAlerts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get students with poor performance who have email addresses
            $studentsWithPoorPerformance = StudentCgpa::poorPerformance()
                ->whereNotNull('email')
                ->where('email', '!=', '')
                ->get();

            Log::info('Found ' . $studentsWithPoorPerformance->count() . ' students with poor performance to notify.');

            foreach ($studentsWithPoorPerformance as $student) {
                try {
                    Mail::to($student->email)->send(new PoorPerformanceAlert($student));
                    
                    Log::info('Poor performance alert sent to student: ' . $student->name . ' (' . $student->email . ')');
                    
                    // Add a small delay to avoid overwhelming the mail server
                    sleep(1);
                    
                } catch (\Exception $e) {
                    Log::error('Failed to send poor performance alert to ' . $student->email . ': ' . $e->getMessage());
                }
            }

            Log::info('Poor performance alert job completed.');
            
        } catch (\Exception $e) {
            Log::error('Poor performance alert job failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
