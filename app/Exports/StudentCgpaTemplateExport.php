<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

class StudentCgpaTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    public function array(): array
    {
        return [
            [
                'STU001',
                '<PERSON>',
                '3.50',
                '3.25',
                '210.50',
                '65.00',
                'Computer Science',
                'Engineering',
                'Semester 6',
                '2020',
                '<EMAIL>'
            ],
            [
                'STU002',
                'Jane Smith',
                '3.80',
                '3.60',
                '228.00',
                '65.00',
                'Information Technology',
                'Engineering',
                'Semester 6',
                '2020',
                '<EMAIL>'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'Index Number',
            'Name',
            'GPA',
            'CGPA',
            'Total Point',
            'Total Credit',
            'Program',
            'Department',
            'Semester',
            'Year of Enrollment',
            'Email'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as header
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4F46E5'],
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // Index Number
            'B' => 25, // Name
            'C' => 10, // GPA
            'D' => 10, // CGPA
            'E' => 15, // Total Point
            'F' => 15, // Total Credit
            'G' => 25, // Program
            'H' => 20, // Department
            'I' => 15, // Semester
            'J' => 20, // Year of Enrollment
            'K' => 30, // Email
        ];
    }
}
