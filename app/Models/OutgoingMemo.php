<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OutgoingMemo extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'outgoing_memos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'date_of_dispatch',
        'registry_no',
        'recipient',
        'date_of_letter',
        'letter_no',
        'subject',
        'remark',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_of_dispatch' => 'date',
        'date_of_letter' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who created this memo.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope a query to search memos.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'like', "%{$search}%")
              ->orWhere('registry_no', 'like', "%{$search}%")
              ->orWhere('recipient', 'like', "%{$search}%")
              ->orWhere('letter_no', 'like', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $fromDate, $toDate)
    {
        if ($fromDate) {
            $query->where('date_of_dispatch', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('date_of_dispatch', '<=', $toDate);
        }
        return $query;
    }

    /**
     * Scope a query to filter by recipient.
     */
    public function scopeToRecipient($query, $recipient)
    {
        if ($recipient) {
            return $query->where('recipient', 'like', "%{$recipient}%");
        }
        return $query;
    }
}
