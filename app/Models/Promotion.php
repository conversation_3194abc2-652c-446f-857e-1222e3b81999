<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'year_of_promotion',
        'end_of_promotion',
    ];

    protected $casts = [
        'year_of_promotion' => 'date',
        'end_of_promotion' => 'date',
    ];

    /**
     * Scope: promotions ending within given days (default 42).
     */
    public function scopeEndingSoon($query, int $days = 42)
    {
        return $query->whereDate('end_of_promotion', '<=', Carbon::now()->addDays($days));
    }
}
