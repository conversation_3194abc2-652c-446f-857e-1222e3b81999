<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IncomingMemo extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'incoming_memos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'date_received',
        'registry_no',
        'from_whom_received',
        'date_of_letter',
        'letter_no',
        'subject',
        'remark',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_received' => 'date',
        'date_of_letter' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user who created this memo.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope a query to search memos.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('subject', 'like', "%{$search}%")
              ->orWhere('registry_no', 'like', "%{$search}%")
              ->orWhere('from_whom_received', 'like', "%{$search}%")
              ->orWhere('letter_no', 'like', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $fromDate, $toDate)
    {
        if ($fromDate) {
            $query->where('date_received', '>=', $fromDate);
        }
        if ($toDate) {
            $query->where('date_received', '<=', $toDate);
        }
        return $query;
    }

    /**
     * Scope a query to filter by sender.
     */
    public function scopeFromSender($query, $sender)
    {
        if ($sender) {
            return $query->where('from_whom_received', 'like', "%{$sender}%");
        }
        return $query;
    }
}
