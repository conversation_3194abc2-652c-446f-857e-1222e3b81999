<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentCgpa extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'student_cgpas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'index_number',
        'name',
        'gpa',
        'cgpa',
        'totalpoint',
        'totalcredit',
        'program',
        'department',
        'semester',
        'year_of_enrollment',
        'email',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'gpa' => 'decimal:2',
        'cgpa' => 'decimal:2',
        'totalpoint' => 'decimal:2',
        'totalcredit' => 'decimal:2',
        'year_of_enrollment' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Scope a query to search students.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('index_number', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by program.
     */
    public function scopeByProgram($query, $program)
    {
        if ($program) {
            return $query->where('program', $program);
        }
        return $query;
    }

    /**
     * Scope a query to filter by department.
     */
    public function scopeByDepartment($query, $department)
    {
        if ($department) {
            return $query->where('department', $department);
        }
        return $query;
    }

    /**
     * Scope a query to filter by semester.
     */
    public function scopeBySemester($query, $semester)
    {
        if ($semester) {
            return $query->where('semester', $semester);
        }
        return $query;
    }

    /**
     * Scope a query to find students with poor performance.
     */
    public function scopePoorPerformance($query)
    {
        return $query->where(function ($q) {
            $q->where('gpa', '<', 1.0)
              ->orWhere('cgpa', '<', 1.5);
        });
    }

    /**
     * Check if student has poor performance.
     */
    public function hasPoorPerformance(): bool
    {
        return $this->gpa < 1.0 || $this->cgpa < 1.5;
    }

    /**
     * Get all unique programs.
     */
    public static function getUniquePrograms()
    {
        return self::distinct()->pluck('program')->filter()->sort()->values();
    }

    /**
     * Get all unique departments.
     */
    public static function getUniqueDepartments()
    {
        return self::distinct()->pluck('department')->filter()->sort()->values();
    }

    /**
     * Get all unique semesters.
     */
    public static function getUniqueSemesters()
    {
        return self::distinct()->pluck('semester')->filter()->sort()->values();
    }
}
