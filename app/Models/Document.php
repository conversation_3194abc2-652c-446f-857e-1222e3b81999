<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_path',
        'file_type',
        'category',
        'user_id',
        'download_count'
    ];

    protected $casts = [
        'download_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFileSizeAttribute()
    {
        $path = storage_path('app/' . $this->file_path);
        if (file_exists($path)) {
            return $this->formatBytes(filesize($path));
        }
        // return 'this file was uploaded '.;
    }

    public function getFileIconAttribute()
    {
        $icons = [
            'pdf' => 'fas fa-file-pdf text-red-500',
            'doc' => 'fas fa-file-word text-blue-500',
            'docx' => 'fas fa-file-word text-blue-500',
            'xls' => 'fas fa-file-excel text-green-500',
            'xlsx' => 'fas fa-file-excel text-green-500',
            'ppt' => 'fas fa-file-powerpoint text-orange-500',
            'pptx' => 'fas fa-file-powerpoint text-orange-500',
        ];

        return $icons[$this->file_type] ?? 'fas fa-file text-gray-500';
    }

    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    public static function getCategories()
    {
        return [
            'meetings' => 'Meetings',
            'memos' => 'Memos',
            'reports' => 'Reports',
            'student-affairs' => 'Student Affairs',
            'staff-affairs' => 'Staff Affairs'
        ];
    }
}