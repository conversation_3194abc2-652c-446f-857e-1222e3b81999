<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PoorPerformanceNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The student's GPA
     *
     * @var float
     */
    public $gpa;

    /**
     * The student's CGPA
     *
     * @var float
     */
    public $cgpa;

    /**
     * The current semester
     *
     * @var string
     */
    public $semester;

    /**
     * Create a new notification instance.
     *
     * @param float $gpa
     * @param float $cgpa
     * @param string $semester
     * @return void
     */
    public function __construct(float $gpa, float $cgpa, string $semester)
    {
        $this->gpa = $gpa;
        $this->cgpa = $cgpa;
        $this->semester = $semester;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $isGpaLow = $this->gpa < 1.0;
        $isCgpaLow = $this->cgpa < 1.5;
        
        $message = (new MailMessage)
            ->subject('Academic Performance Notice - ' . config('app.name'))
            ->greeting('Dear ' . $notifiable->name . ',');
            
        if ($isGpaLow && $isCgpaLow) {
            $message->line('We are writing to express our concern about your academic performance this semester. ')
                   ->line('Your current GPA (' . number_format($this->gpa, 2) . ') is below the minimum threshold of 1.0, and your CGPA (' . number_format($this->cgpa, 2) . ') is also below the required 1.5.');
        } elseif ($isGpaLow) {
            $message->line('We are writing to express our concern about your academic performance this semester. ')
                   ->line('Your current GPA (' . number_format($this->gpa, 2) . ') is below the minimum threshold of 1.0.');
        } else {
            $message->line('We are writing to express our concern about your academic performance. ')
                   ->line('Your current CGPA (' . number_format($this->cgpa, 2) . ') is below the required threshold of 1.5.');
        }
        
        $message->line('')
               ->line('This is an automated notification to inform you that your academic performance requires attention. ')
               ->line('We encourage you to:')
               ->line('- Meet with your academic advisor')
               ->line('- Attend all classes and submit all assignments on time')
               ->line('- Seek help from tutors or study groups')
               ->line('- Utilize the academic support services available on campus')
               ->line('')
               ->action('View Academic Resources', url('/academic-resources'))
               ->line('')
               ->line('If you have any questions or need assistance, please do not hesitate to contact your academic advisor or the student support services.')
               ->line('')
               ->salutation('Best regards,\n' . config('app.name') . ' Academic Team');
        
        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'gpa' => $this->gpa,
            'cgpa' => $this->cgpa,
            'semester' => $this->semester,
            'message' => 'Poor academic performance notification sent',
        ];
    }
}
