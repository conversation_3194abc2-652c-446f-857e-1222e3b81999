<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class NewUserCredentials extends Mailable
{
    public User $user;
    public string $plainPassword;

    public function __construct(User $user, string $plainPassword)
    {
        $this->user = $user;
        $this->plainPassword = $plainPassword;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your FBNE Account Credentials',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.users.credentials',
            with: [
                'name'     => $this->user->name,
                'email'    => $this->user->email,
                'role'     => $this->user->role,
                'password' => $this->plainPassword,
            ],
        );
    }
}
