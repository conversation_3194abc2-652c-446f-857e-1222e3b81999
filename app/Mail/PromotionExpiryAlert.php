<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PromotionExpiryAlert extends Mailable
{
    use Queueable, SerializesModels;

    public array $promotions;
    public int $days;

    /**
     * Create a new message instance.
     */
    public function __construct($promotions, int $days)
    {
        $this->promotions = $promotions;
        $this->days = $days;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Promotions expiring within ' . $this->days . ' days')
                    ->markdown('emails.promotions.expiry_alert');
    }
}
