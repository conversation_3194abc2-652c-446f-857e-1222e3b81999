<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class LoginOtp extends Mailable
{
    public User $user;
    public string $otp;

    public function __construct(User $user, string $otp)
    {
        $this->user = $user;
        $this->otp = $otp;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'FBNE LOGIN OTP',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.auth.otp',
            with: [
                'name' => $this->user->name,
                'otp'  => $this->otp,
            ],
        );
    }
}
