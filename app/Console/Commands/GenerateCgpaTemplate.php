<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CgpaTemplateExport;

class GenerateCgpaTemplate extends Command
{
    protected $signature = 'app:generate-cgpa-template';
    protected $description = 'Generate CGPA import template Excel file';

    public function handle()
    {
        $directory = public_path('templates');
        
        // Ensure the directory exists
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
        
        $filename = 'cgpa_import_template.xlsx';
        $path = "{$directory}/{$filename}";
        
        // Generate the Excel file
        Excel::store(new CgpaTemplateExport(), "templates/{$filename}", 'public');
        
        // Set proper permissions
        chmod($path, 0644);
        
        $this->info("CGPA template generated at: {$path}");
        return 0;
    }
}
