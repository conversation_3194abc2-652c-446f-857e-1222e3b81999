<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Promotion;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\PromotionExpiryAlert;

class SendPromotionExpiryAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'promotions:alert {--days=42 : Number of days ahead to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email alerts for promotions ending within N days (default 42).';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');

        $expiring = Promotion::endingSoon($days)->orderBy('end_of_promotion')->get();

        if ($expiring->isEmpty()) {
            $this->info('No promotions expiring within ' . $days . ' days.');
            return Command::SUCCESS;
        }

        $recipients = User::where('role', 'admin')->pluck('email')->filter()->all();
        if (empty($recipients)) {
            $this->warn('No admin recipients found.');
            return Command::SUCCESS;
        }

        Mail::to($recipients)->send(new PromotionExpiryAlert($expiring, $days));

        $this->info('Alert sent to ' . count($recipients) . ' admin user(s).');
        return Command::SUCCESS;
    }
}
