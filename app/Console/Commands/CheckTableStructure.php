<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CheckTableStructure extends Command
{
    protected $signature = 'table:check {table} {--columns}';
    protected $description = 'Check the structure of a database table';

    public function handle()
    {
        $table = $this->argument('table');
        $showColumns = $this->option('columns');

        if (!Schema::hasTable($table)) {
            $this->error("Table {$table} does not exist!");
            return 1;
        }

        $this->info("Table '{$table}' exists.");
        
        if ($showColumns) {
            $columns = DB::select("SHOW COLUMNS FROM {$table}");
            $this->table(
                ['Field', 'Type', 'Null', 'Key', 'Default', 'Extra'],
                array_map(function($col) {
                    return [
                        'Field' => $col->Field,
                        'Type' => $col->Type,
                        'Null' => $col->Null,
                        'Key' => $col->Key,
                        'Default' => $col->Default,
                        'Extra' => $col->Extra
                    ];
                }, $columns)
            );
        }

        $indexes = DB::select("SHOW INDEX FROM {$table}");
        if (count($indexes) > 0) {
            $this->info("\nIndexes:");
            $this->table(
                ['Key_name', 'Column_name', 'Non_unique', 'Index_type'],
                array_map(function($index) {
                    return [
                        'Key_name' => $index->Key_name,
                        'Column_name' => $index->Column_name,
                        'Non_unique' => $index->Non_unique,
                        'Index_type' => $index->Index_type
                    ];
                }, $indexes)
            );
        }

        return 0;
    }
}
