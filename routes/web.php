<?php

use App\Http\Controllers\Auth\OtpController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DocumentController;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;


Route::get('/', fn () => view('welcome'));

Route::get('/dashboard', function () {
    $role = strtolower(Auth::user()->role ?? 'guest');
    return redirect()->route(match ($role) {
        'admin' => 'admin.dashboard',
        'secretary' => 'secretary.dashboard',
        default => 'admin.dashboard',
    });
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])
         ->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])
         ->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])
         ->name('profile.destroy');
});




// OTP routes
Route::middleware('web')->group(function () {
    Route::get('/otp', [OtpController::class, 'create'])->name('otp.create');
    Route::post('/otp', [OtpController::class, 'store'])->name('otp.store');
    Route::post('/otp/resend', [OtpController::class, 'resend'])->name('otp.resend');
});



// Dashboards
Route::middleware(['auth', 'role:admin'])->group(function () {
    // Admin Dashboard
    Route::get('/admin/dashboard', fn () => view('dashboard'))->name('admin.dashboard');
});

Route::middleware(['auth', 'role:secretary'])->group(function () {
    Route::get('/secretary/dashboard', [\App\Http\Controllers\SecretaryDashboardController::class, 'index'])->name('secretary.dashboard');
});

// Other shared authenticated routes

    // Activity log dedicated page
    Route::get('/activity_log', function (\Illuminate\Http\Request $request) {
        $q = $request->query('q');
        $perPage = $request->query('per_page', 8);
        $ajax = $request->query('ajax');
        
        $logsQuery = ActivityLog::with('user');
        if (strtolower(Auth::user()->role) === 'secretary') {
            $logsQuery->where('user_id', Auth::id());
        }
        $logs = $logsQuery
            ->when($q, function ($query) use ($q) {
                $query->where('action', 'like', "%{$q}%")
                      ->orWhere('role', 'like', "%{$q}%")
                      ->orWhereHas('user', fn ($u) => $u->where('name', 'like', "%{$q}%"));
            })
            ->latest()
            ->paginate($perPage);

        if ($ajax) {
            return response()->json([
                'html' => view('activity_log.partials.table', compact('logs'))->render(),
                'count' => $logs->total()
            ]);
        }

        return view('activity_log.index', compact('logs'));
    })->middleware('auth')->name('activity_log.index');

Route::get('/documents', [DocumentController::class, 'index'])->name('documents.index');
Route::post('/documents', [DocumentController::class, 'store'])->name('documents.store');
Route::get('/documents/{document}/download', [DocumentController::class, 'download'])->name('documents.download');
Route::get('/documents/{document}/preview', [DocumentController::class, 'preview'])->name('documents.preview');
Route::delete('/documents/{document}', [DocumentController::class, 'destroy'])->name('documents.destroy');
Route::post('/documents/bulk-download', [DocumentController::class, 'bulkDownload'])->name('documents.bulk-download');
Route::get('/documents/stats', [DocumentController::class, 'getStats'])->name('documents.stats');
// User Management routes
Route::middleware('auth')->resource('users', \App\Http\Controllers\UserController::class)->except(['show']);

// Admin-only routes for Promotion and Student management
Route::middleware(['auth', 'role:admin'])->group(function () {
    // Export students CSV - MOVED BEFORE resource route to prevent conflicts
    Route::get('students/export', [\App\Http\Controllers\StudentController::class, 'export'])->name('students.export');
    Route::post('students/import', [\App\Http\Controllers\StudentController::class, 'import'])->name('students.import');
    Route::resource('promotions', \App\Http\Controllers\PromotionController::class);
    Route::resource('students', \App\Http\Controllers\StudentController::class);

    // Memo Management Routes
    Route::get('/incoming-memos', function () {
        return view('memos.incoming');
    })->name('memos.incoming');

    Route::get('/outgoing-memos', function () {
        return view('memos.outgoing');
    })->name('memos.outgoing');

    // Student CGPA Management Routes
    Route::get('/student-cgpa', function () {
        return view('student-cgpa.index');
    })->name('student-cgpa.index');
});

require __DIR__ . '/auth.php';

