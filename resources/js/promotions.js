document.addEventListener('alpine:init', () => {
    // Only register if not already registered
    if (window.Alpine && window.Alpine.data('promotions')) return;
    
    // Register the component
    window.Alpine.data('promotions', () => ({
        // Filter state
        filters: {
            position: '',
            year: '',
            search: ''
        },
        
        // Modal states
        showModal: false,
        showDeleteModal: false,
        itemToDelete: null,
        
        // Form state
        form: {
            id: null,
            name: '',
            position: '',
            year_of_promotion: '',
            end_of_promotion: ''
        },
        errors: {},
        loading: false,
        
        // Initialize component
        init() {
            // Load any saved filters from localStorage
            const savedFilters = localStorage.getItem('promotionFilters');
            if (savedFilters) {
                this.filters = JSON.parse(savedFilters);
                // Apply filters immediately if we have any
                if (Object.values(this.filters).some(<PERSON>olean)) {
                    // Apply filters immediately using the helper that updates the URL & fetches results
                    this.applyFilters();
                }
            }
            
            // Listen for filter changes
            this.$watch('filters', (newVal) => {
                localStorage.setItem('promotionFilters', JSON.stringify(newVal));
                // Debounce the filter application
                if (this.filterTimeout) clearTimeout(this.filterTimeout);
                this.filterTimeout = setTimeout(() => {
                    this.applyFilters();
                }, 300);
            }, { deep: true });
        },
        
        // Open modal for adding/editing
        openModal(promotion = null) {
            if (promotion) {
                // Edit mode
                this.form = {
                    id: promotion.id,
                    name: promotion.name,
                    position: promotion.position,
                    // Assuming dates are already in 'YYYY-MM-DD' format from the server/Blade
                    year_of_promotion: promotion.year_of_promotion, 
                    end_of_promotion: promotion.end_of_promotion
                };
            } else {
                // Add mode
                this.form = {
                    id: null,
                    name: '',
                    position: '',
                    year_of_promotion: new Date().toISOString().split('T')[0],
                    end_of_promotion: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1 year from now
                };
            }
            this.errors = {};
            this.$dispatch('open-modal', 'promotion-modal');
            
            // Focus the first input when modal opens
            this.$nextTick(() => {
                const firstInput = this.$el.querySelector('#name');
                if (firstInput) firstInput.focus();
            });
        },
        
        // Submit the form
        async submitForm(event) {
            event.preventDefault();
            this.loading = true;
            this.errors = {};
            
            const url = this.form.id 
                ? `/promotions/${this.form.id}`
                : '/promotions';
            const method = this.form.id ? 'PUT' : 'POST';
            
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(this.form)
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    if (response.status === 422) {
                        // Validation errors
                        this.errors = data.errors || {};
                        throw new Error('Please fix the form errors');
                    }
                    throw new Error(data.message || 'An error occurred');
                }
                
                // Show success message
                this.$dispatch('notify', {
                    type: 'success',
                    message: data.message || (this.form.id ? 'Promotion updated successfully' : 'Promotion created successfully')
                });
                
                // Close modal and refresh data
                this.$dispatch('close-modal', 'promotion-modal');
                this.applyFilters();
                
            } catch (error) {
                console.error('Error saving promotion:', error);
                if (!this.errors || Object.keys(this.errors).length === 0) {
                    this.$dispatch('notify', {
                        type: 'error',
                        message: error.message || 'An error occurred while saving the promotion'
                    });
                }
            } finally {
                this.loading = false;
            }
        },
        
        // Open delete confirmation modal
        confirmDelete(promotion) {
            this.itemToDelete = promotion;
            this.$dispatch('open-modal', 'delete-promotion-modal');
        },
        
        // Close delete confirmation modal (called after successful delete or cancel)
        closeDeleteModal() {
            this.$dispatch('close-modal', 'delete-promotion-modal');
            this.itemToDelete = null;
        },
        
        // Delete a promotion
        async deletePromotion() {
            if (!this.itemToDelete) return;
            
            this.loading = true;
            
            try {
                const response = await fetch(`/promotions/${this.itemToDelete.id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Failed to delete promotion');
                }
                
                // Show success message
                this.$dispatch('notify', {
                    type: 'success',
                    message: data.message || 'Promotion deleted successfully'
                });
                
                // Close modal and refresh data
                this.closeDeleteModal();
                this.applyFilters();
                
            } catch (error) {
                console.error('Error deleting promotion:', error);
                this.$dispatch('notify', {
                    type: 'error',
                    message: error.message || 'An error occurred while deleting the promotion'
                });
            } finally {
                this.loading = false;
            }
        },
        
    
        // Apply filters to the table and update URL
        applyFilters() {
            const params = new URLSearchParams();
            
            if (this.filters.position) {
                params.append('position', this.filters.position);
            }
            
            if (this.filters.year) {
                // Ensure year is a valid number
                const year = parseInt(this.filters.year);
                if (!isNaN(year) && year > 1900 && year < 2100) {
                    params.append('year', year);
                } else if (this.filters.year.trim() !== '') {
                    // If invalid year but not empty, don't submit
                    return;
                }
            }
            
            if (this.filters.search) {
                params.append('q', this.filters.search);
            }
            
            // Update URL without reloading the page
            const newUrl = `${window.location.pathname}?${params.toString()}`;
            window.history.pushState({}, '', newUrl);
            
            // Make an AJAX request to get filtered results
            this.loadFilteredResults(newUrl);
        },
        
        // Load filtered results via AJAX
        async loadFilteredResults(url) {
            try {
                this.loading = true;
                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) throw new Error('Failed to load filtered results');
                
                const html = await response.text();
                // Update the table with the new HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newContainer = doc.querySelector('#promotion-table-container');
                if (newContainer) {
                    const oldContainer = document.getElementById('promotion-table-container');
                    if (oldContainer) {
                        oldContainer.replaceWith(newContainer);
                    }
                }
            } catch (error) {
                console.error('Error loading filtered results:', error);
                this.$dispatch('notify', {
                    type: 'error',
                    message: 'Error loading filtered results. Please try again.'
                });
            } finally {
                this.loading = false;
            }
        },
        
        // Reset all filters
        resetFilters() {
            this.filters = {
                position: '',
                year: '',
                search: ''
            };
       
        }
    }));
});
