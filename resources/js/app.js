import './bootstrap';
import Alpine from 'alpinejs';
import 'flowbite';

// Initialize Alpine.js
window.Alpine = Alpine;

// Theme store
document.addEventListener('alpine:init', () => {
    Alpine.store('theme', {
        dark: localStorage.getItem('darkMode') === 'true',

        init() {
            // Apply theme on load
            this.applyTheme();
            
            // Watch for system preference changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                if (!('darkMode' in localStorage)) {
                    this.dark = e.matches;
                    this.applyTheme();
                }
            });
        },

        toggle() {
            this.dark = !this.dark;
            localStorage.setItem('darkMode', this.dark);
            this.applyTheme();
        },

        applyTheme() {
            if (this.dark) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    });
});

// Start Alpine.js
Alpine.start();

// Import other components after Alpine is initialized
import './promotions.js';

// Global listener: convert Alpine $dispatch('notify', {type, message, title?, options?}) to Flasher toast
window.addEventListener('notify', (event) => {
    if (typeof window.fireToast === 'function') {
        const { type = 'success', message = '', title = '', options = {} } = event.detail || {};
        window.fireToast(type, message, title, options);
    }
});

