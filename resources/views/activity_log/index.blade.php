@extends('layouts.app')

@section('main')

    <div class="py-6 max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="mb-4 flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <!-- Dynamic Search -->
                    <div class="relative">
                        <input type="text" id="searchInput" 
                               placeholder="Search role, user or action..." 
                               value="{{ request('q') }}" 
                               class="w-full md:w-96 px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 dark:text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <div id="searchSpinner" class="absolute inset-y-0 right-0 pr-3 flex items-center hidden">
                            <i class="fas fa-spinner fa-spin text-white"></i>
                        </div>
                    </div>
                    
                    <!-- Per Page Selector -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500 dark:text-gray-400">Show</span>
                        <select id="perPage" 
                                class="block w-24 border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                            <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                            <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                        </select>
                        <span class="text-sm text-gray-500 dark:text-gray-400">per page</span>
                    </div>
                </div>
                
                <!-- Total Records -->
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Showing {{ $logs->firstItem() }} to {{ $logs->lastItem() }} of {{ $logs->total() }} entries
                </div>
            </div>

            <!-- Table Container -->
            <div id="tableContainer">
                @include('activity_log.partials.table', ['logs' => $logs])
            </div>
        </div>
    </div>
    @endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const perPage = document.getElementById('perPage');
    const searchSpinner = document.getElementById('searchSpinner');
    const tableContainer = document.getElementById('tableContainer');

    // Debounced search function
    let searchTimeout;
    function performSearch() {
        const searchTerm = searchInput.value.trim();
        const itemsPerPage = perPage.value;
        
        searchSpinner.classList.remove('hidden');
        
        const params = new URLSearchParams();
        if (searchTerm) params.append('q', searchTerm);
        if (itemsPerPage) params.append('per_page', itemsPerPage);
        
        fetch(`{{ route('activity_log.index') }}?${params.toString()}&ajax=1`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Update the table container with new content
            tableContainer.innerHTML = data.html;
            
            // Update URL without page reload
            const newUrl = new URL(window.location.href);
            newUrl.search = params.toString();
            history.pushState({}, '', newUrl);
            
            searchSpinner.classList.add('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            searchSpinner.classList.add('hidden');
        });
    }

    // Add event listeners
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 300); // 300ms delay
    });

    perPage.addEventListener('change', performSearch);

    // Handle pagination clicks
    tableContainer.addEventListener('click', function(e) {
        if (e.target.closest('.page-link')) {
            e.preventDefault();
            const page = e.target.closest('.page-link').dataset.page;
            
            const params = new URLSearchParams(window.location.search);
            params.set('page', page);
            
            fetch(`{{ route('activity_log.index') }}?${params.toString()}&ajax=1`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                tableContainer.innerHTML = data.html;
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    });
});
</script>
@endpush