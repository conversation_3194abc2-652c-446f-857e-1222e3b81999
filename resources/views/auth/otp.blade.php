<x-guest-layout>
    <div class="mb-4 text-sm text-gray-600 dark:text-gray-400">
        {{ __('Please enter the One-Time Password (OTP) sent to your email. The code expires in 5 minutes.') }}
    </div>

    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form method="POST" action="{{ route('otp.store') }}">
        @csrf

        <!-- OTP -->
        <div>
            <x-input-label for="otp" :value="__('OTP')" />

            <x-text-input id="otp" class="block mt-1 w-full" type="text" name="otp" required autofocus />

            <x-input-error :messages="$errors->get('otp')" class="mt-2" />
        </div>

        <div class="flex items-center justify-end mt-4">
            <button formaction="{{ route('otp.resend') }}" formmethod="POST" formnovalidate class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Resend OTP</button>

            <x-primary-button class="ml-4">
                {{ __('Verify') }}
            </x-primary-button>
        </div>
    </form>
</x-guest-layout>
