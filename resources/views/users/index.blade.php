@extends('layouts.app')

@section('main')

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search Filters -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Name Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Name</label>
                        <input type="text" id="searchName" 
                               class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-indigo-500 focus:border-indigo-500" 
                               placeholder="Search by name...">
                    </div>

                    <!-- Role Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Role</label>
                        <select id="searchRole" 
                                class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">All Roles</option>
                            @foreach(['Admin','Accountant','Secretary','HOD'] as $role)
                                <option value="{{ $role }}">{{ $role }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Email Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Email</label>
                        <input type="email" id="searchEmail" 
                               class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white focus:ring-indigo-500 focus:border-indigo-500" 
                               placeholder="Search by email...">
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">{{ __('Users') }}</h3>
                    <button
                        @click="$dispatch('open-modal', 'add-user-modal')"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        {{ __('Add User') }}
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Full Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email Verified</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @forelse ($users as $user)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $user->role }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $user->name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $user->email }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        @if($user->email_verified_at)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">Verified</span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">Pending</span>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button @click="$dispatch('open-modal', 'edit-user-{{ $user->id }}')" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 rounded-md text-sm">Edit</button>

                                        <button @click="$dispatch('open-modal', 'delete-user-{{ $user->id }}')" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 rounded-md text-sm">Delete</button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">No users found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $users->links() }}
                </div>
            </div>
        </div>
    </div>
    <!-- Add User Modal -->
    <x-modal name="add-user-modal" focusable>
        <form method="POST" action="{{ route('users.store') }}" class="p-6 space-y-4">
            @csrf
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-black">Role</label>
                <select name="role" class="mt-1 block w-full border-gray-300 rounded-md  dark:border-gray-700 dark:text-black" required>
                    @foreach(['Admin','Accountant','Secretary','HOD'] as $role)
                        <option value="{{ $role }}">{{ $role }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-black">Full Name</label>
                <input type="text" name="name" class="mt-1 block w-full border-gray-300 rounded-md  dark:border-gray-700 dark:text-black" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-black">Email</label>
                <input type="email" name="email" class="mt-1 block w-full border-gray-300 rounded-md  dark:border-gray-700 dark:text-black" required>
            </div>
            <!-- Password will be generated automatically and emailed to the user -->
            <div class="flex justify-end space-x-2">
                <button type="button" @click="$dispatch('close-modal', 'add-user-modal')" class="px-4 py-2 bg-gray-100  rounded-md text-sm">Cancel</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm">Save</button>
            </div>
        </form>
    </x-modal>

    <!-- Edit & Delete Modals per User -->
    @foreach($users as $modalUser)
        <!-- Edit User Modal -->
        <x-modal name="edit-user-{{ $modalUser->id }}" focusable>
            <form method="POST" action="{{ route('users.update', $modalUser) }}" class="p-6 space-y-4">
                @csrf
                @method('PUT')
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-black">Role</label>
                    <select name="role" class="mt-1 block w-full border-gray-300 rounded-md dark:border-black dark:text-black" required>
                        @foreach(['Admin','Accountant','Secretary','HOD'] as $role)
                            <option value="{{ $role }}" @selected(old('role', $modalUser->role) == $role)>{{ $role }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-black">Full Name</label>
                    <input type="text" name="name" value="{{ old('name', $modalUser->name) }}" class="mt-1 block w-full border-gray-300 rounded-md  dark:border-gray-700 dark:text-black" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-black">Email</label>
                    <input type="email" name="email" value="{{ old('email', $modalUser->email) }}" class="mt-1 block w-full border-gray-300 rounded-md  dark:border-black dark:text-black" required>
                </div>
                <!-- Password changes are not allowed here -->
                <div class="flex justify-end space-x-2">
                    <button type="button" @click="$dispatch('close-modal', 'edit-user-{{ $modalUser->id }}')" class="px-4 py-2 bg-gray-100 rounded-md text-sm">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-700 text-white rounded-md text-sm">Update</button>
                </div>
            </form>
        </x-modal>

        <!-- Delete User Modal -->
        <x-modal name="delete-user-{{ $modalUser->id }}">
            <form method="POST" action="{{ route('users.destroy', $modalUser) }}" class="p-6 space-y-4">
                @csrf
                @method('DELETE')
                <p class="text-sm text-gray-600 dark:text-black">Are you sure you want to delete <span class="font-semibold">{{ $modalUser->name }}</span>? This action cannot be undone.</p>

                <div class="flex justify-end space-x-2">
                    <button type="button" @click="$dispatch('close-modal', 'delete-user-{{ $modalUser->id }}')" class="px-4 py-2 dark:bg-blue-700 text-white rounded-md text-sm">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm">Delete</button>
                </div>
            </form>
        </x-modal>
    @endforeach
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchName = document.getElementById('searchName');
        const searchRole = document.getElementById('searchRole');
        const searchEmail = document.getElementById('searchEmail');
        const rows = document.querySelectorAll('tbody tr');

        // Function to filter rows
        function filterRows() {
            const nameTerm = searchName.value.toLowerCase();
            const roleTerm = searchRole.value.toLowerCase();
            const emailTerm = searchEmail.value.toLowerCase();

            rows.forEach(row => {
                const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const role = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const email = row.querySelector('td:nth-child(4)').textContent.toLowerCase();

                const matchesName = name.includes(nameTerm);
                const matchesRole = roleTerm === '' || role.includes(roleTerm);
                const matchesEmail = email.includes(emailTerm);

                row.style.display = matchesName && matchesRole && matchesEmail ? '' : 'none';
            });
        }

        // Add event listeners
        searchName.addEventListener('input', filterRows);
        searchRole.addEventListener('change', filterRows);
        searchEmail.addEventListener('input', filterRows);
    });
</script>
@endpush

