@extends('layouts.app')

@section('main')
<div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between mb-6" x-data="promotions">
        <h2 class="text-2xl font-semibold text-gray-900">Promotions</h2>
        <button 
            type="button"
            @click="openModal()"
            class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
        >
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add Promotion
        </button>
    </div>

    <!-- Flash messages -->
    <div class="mb-6">
        <x-flasher-messages />
    </div>

    <div x-data="promotions" x-init="init()">
            <!-- Filter / Search Bar -->
            <div class="bg-white shadow px-4 py-3 rounded-lg mb-6 flex flex-wrap items-center gap-4">
                <!-- Search -->
                <div class="flex-1 min-w-[180px]">
                    <label class="sr-only" for="search">Search</label>
                    <input
                        type="text"
                        id="search"
                        placeholder="Search by name..."
                        x-model="filters.search"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                </div>
                <!-- Position -->
                <div>
                    <label class="sr-only" for="filter_position">Position</label>
                    <select
                        id="filter_position"
                        x-model="filters.position"
                        class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                        <option value="">All Positions</option>
                        <template x-for="pos in ['Dean','Exam Officer','Program Coordinator','SAA','Faculty Officer','HOD']" :key="pos">
                            <option :value="pos" x-text="pos"></option>
                        </template>
                    </select>
                </div>
                <!-- Year -->
                <div class="w-24">
                    <label class="sr-only" for="filter_year">Year</label>
                    <input
                        type="number"
                        id="filter_year"
                        placeholder="Year"
                        x-model="filters.year"
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                </div>
                <!-- Reset Button -->
                <button
                    type="button"
                    @click="resetFilters"
                    class="inline-flex items-center px-3 py-2 bg-gray-100 text-sm rounded-md hover:bg-gray-200"
                >
                    Reset
                </button>
            </div>
        @if($promotions->count())
            <div id="promotion-table-container" class="overflow-x-auto bg-white shadow rounded-lg">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($promotions as $promotion)
                            @php
                                // Ensure dates are formatted correctly for JS if they are Carbon instances
                                $promotionData = $promotion->toArray();
                                if ($promotion->year_of_promotion instanceof \Carbon\Carbon) {
                                    $promotionData['year_of_promotion'] = $promotion->year_of_promotion->toDateString();
                                }
                                if ($promotion->end_of_promotion instanceof \Carbon\Carbon) {
                                    $promotionData['end_of_promotion'] = $promotion->end_of_promotion->toDateString();
                                }
                            @endphp
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ $promotion->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $promotion->position }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ $promotion->year_of_promotion ? \Carbon\Carbon::parse($promotion->year_of_promotion)->format('M d, Y') : 'N/A' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ $promotion->end_of_promotion ? \Carbon\Carbon::parse($promotion->end_of_promotion)->format('M d, Y') : 'N/A' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button 
                                        type="button"
                                        @click="openModal({{ json_encode($promotionData) }})"
                                        class="text-blue-600 hover:text-blue-900 mr-4"
                                    >
                                        <svg class="h-5 w-5 inline" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                        </svg>
                                        Edit
                                    </button>
                                    <button 
                                        type="button"
                                        @click="confirmDelete({{ json_encode($promotionData) }})"
                                        class="text-red-600 hover:text-red-900"
                                    >
                                        <svg class="h-5 w-5 inline" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="mt-4">{{ $promotions->links() }}</div>
        @else
            <div id="promotion-table-container" class="flex flex-col items-center justify-center py-20 bg-white shadow rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-3-3v6m5 4a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="mt-4 text-gray-500 text-lg">No promotions found.</p>
            </div>
        @endif

        <!-- Add/Edit Promotion Modal -->
        <x-modal name="promotion-modal" :show="false" maxWidth="2xl">
    <div class="px-6 py-4">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="modal-title">
            <span x-text="form.id ? 'Edit Promotion' : 'Add New Promotion'"></span>
        </h3>
        
        <form @submit.prevent="submitForm($event)" class="space-y-4">
            <!-- Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                <input
                    type="text"
                    id="name"
                    x-model="form.name"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                    :disabled="loading"
                >
                <p x-show="errors.name" x-text="errors.name[0]" class="mt-1 text-sm text-red-600"></p>
            </div>

            <!-- Position -->
            <div>
                <label for="position" class="block text-sm font-medium text-gray-700">Position</label>
                <select
                    id="position"
                    x-model="form.position"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                    :disabled="loading"
                >
                    <option value="">Select Position</option>
                    @foreach(['Dean','Exam Officer','Program Coordinator','SAA','Faculty Officer','HOD'] as $pos)
                        <option value="{{ $pos }}">{{ $pos }}</option>
                    @endforeach
                </select>
                <p x-show="errors.position" x-text="errors.position[0]" class="mt-1 text-sm text-red-600"></p>
            </div>

            <!-- Year of Promotion -->
            <div>
                <label for="year_of_promotion" class="block text-sm font-medium text-gray-700">Start Date</label>
                <input
                    type="date"
                    id="year_of_promotion"
                    x-model="form.year_of_promotion"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                    :disabled="loading"
                >
                <p x-show="errors.year_of_promotion" x-text="errors.year_of_promotion[0]" class="mt-1 text-sm text-red-600"></p>
            </div>

            <!-- End of Promotion -->
            <div>
                <label for="end_of_promotion" class="block text-sm font-medium text-gray-700">End Date</label>
                <input
                    type="date"
                    id="end_of_promotion"
                    x-model="form.end_of_promotion"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    required
                    :disabled="loading"
                >
                <p x-show="errors.end_of_promotion" x-text="errors.end_of_promotion[0]" class="mt-1 text-sm text-red-600"></p>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <button
                    type="button"
                    @click="$dispatch('close-modal', 'promotion-modal')"
                    class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    :disabled="loading"
                >
                    Cancel
                </button>
                <button
                    {{-- @click="$dispatch('close-modal', 'promotion-modal')" --}}
                    type="submit"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    :disabled="loading"
                >
                    <span x-show="!loading" x-text="form.id ? 'Update' : 'Create'"></span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="form.id ? 'Updating...' : 'Saving...'"></span>
                    </span>
                </button>
            </div>
        </form>
    </div>
</x-modal>

<!-- Delete Confirmation Modal -->
<x-modal name="delete-promotion-modal" :show="false" maxWidth="lg">
    <div class="px-6 py-4">
        <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Delete Promotion
                </h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500">
                        Are you sure you want to delete the promotion for <span x-text="itemToDelete ? itemToDelete.name : ''" class="font-medium"></span>?
                        This action cannot be undone.
                    </p>
                </div>
            </div>
        </div>
        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button
                type="button"
                @click="deletePromotion()"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                :disabled="loading"
            >
                <span x-show="!loading">Delete</span>
                <span x-show="loading" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                </span>
            </button>
            <button
                type="button"
                @click="$dispatch('close-modal', 'delete-promotion-modal')"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                :disabled="loading"
            >
                Cancel
            </button>
        </div>
    </div>
</x-modal>

    @push('styles')
    <style>
        [x-cloak] { display: none !important; }
    </style>
    @endpush

@endsection
