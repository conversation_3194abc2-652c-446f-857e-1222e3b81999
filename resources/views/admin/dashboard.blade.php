@extends('layouts.app')

@section('main')
    <!-- Welcome Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-10">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome back, {{ Auth::user()->name }}! 👋</h1>
                <p class="mt-1 text-gray-600 dark:text-gray-300">You + this dashboard = unstoppable team</p>
            </div>
            <div class="mt-4 md:mt-0">
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
                    <span class="w-2 h-2 mr-2 rounded-full bg-blue-500"></span>
                    {{ now()->format('l, F j, Y') }}
                </span>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
        <!-- CGPA Tracker -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 p-6 group">
            <div class="flex items-center justify-between">
                <div class="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">CGPA</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">3.45</p>
                    <p class="text-sm text-green-600 dark:text-green-400">+0.15 from last term</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: 69%"></div>
                </div>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">3.45 / 4.0</p>
            </div>
        </div>

        <!-- Pending Tasks -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 p-6 group">
            <div class="flex items-center justify-between">
                <div class="p-3 rounded-lg bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Student</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">8</p>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 p-6 group">
            <div class="flex items-center justify-between">
                <div class="p-3 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Outgoing Memos</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">12</p>
                </div>
            </div>
        </div>

        <!-- Upcoming Deadlines -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 p-6 group">
            <div class="flex items-center justify-between">
                <div class="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Incoming Memo</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">5</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 gap-6">
        {{-- <!-- Recent Activities (Activity Log) -->
        <div class="mb-4 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Activity Log</h2>
            <form method="GET" action="{{ route('admin.dashboard') }}" class="flex items-center">
                <input type="text" name="q" value="{{ $q ?? '' }}" placeholder="Search..." class="px-3 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm rounded-r-md hover:bg-blue-700">Go</button>
            </form>
        </div>
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-dark:bg-gray-800 border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">When</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($logs as $log)
                            <tr class="border-b border-gray-200 dark:border-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $log->role }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $log->user->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ optional($log->user->last_login_at)->format('Y-m-d H:i') ?? '—' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ ucfirst($log->action) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ $log->created_at->diffForHumans() }}</td>
                            </tr>
                        @empty
                            <tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No activities yet.</td></tr>
                        @endforelse
                    </tbody>
                </table>
                <div class="mt-2">{{ $logs->links() }}</div>
            </div>
            
        </div>

        --}}
        <!-- Quick Links -->
        <div class="space-y-6">
            <!-- CGPA Tracker -->
            <!-- <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">CGPA Tracker</h2>
                <div class="space-y-4">
                    @foreach([
                        ['course' => 'Advanced Database', 'grade' => 'A', 'progress' => '90', 'color' => 'bg-blue-500'],
                        ['course' => 'Web Development', 'grade' => 'B+', 'progress' => '85', 'color' => 'bg-emerald-500'],
                        ['course' => 'AI & ML', 'grade' => 'A-', 'progress' => '88', 'color' => 'bg-purple-500'],
                        ['course' => 'Data Structures', 'grade' => 'B+', 'progress' => '82', 'color' => 'bg-amber-500'],
                    ] as $course)
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="font-medium text-gray-700 dark:text-gray-200">{{ $course['course'] }}</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ $course['grade'] }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                            <div class="h-2 rounded-full {{ $course['color'] }}" style="width: {{ $course['progress'] }}%"></div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div> -->

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
                <div class="grid grid-cols-2 gap-3">
                    @foreach([
                        ['icon' => 'M12 6v6m0 0v6m0-6h6m-6 0H6', 'label' => 'New Task', 'color' => 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'],
                        ['icon' => 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z', 'label' => 'Pay Fees', 'color' => 'bg-emerald-100 text-emerald-600 dark:bg-emerald-900/30 dark:text-emerald-400'],
                        ['icon' => 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z', 'label' => 'Schedule', 'color' => 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400'],
                        ['icon' => 'M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z', 'label' => 'Messages', 'color' => 'bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400'],
                    ] as $action)
                    <button class="flex flex-col items-center justify-center p-4 rounded-xl {{ $action['color'] }} hover:opacity-90 transition-opacity">
                        <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $action['icon'] }}" />
                        </svg>
                        <span class="text-sm font-medium">{{ $action['label'] }}</span>
                    </button>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
