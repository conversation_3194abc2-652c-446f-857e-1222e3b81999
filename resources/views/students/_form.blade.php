<div class="space-y-6">
    <div>
        <label class="block text-sm font-medium mb-1">Name</label>
        <input type="text" name="student_name" value="{{ old('student_name', $student->student_name ?? '') }}" class="w-full border-gray-300 rounded-lg">
        @error('student_name')<p class="text-red-600 text-sm">{{ $message }}</p>@enderror
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium mb-1">Department</label>
            <input type="text" name="department" value="{{ old('department', $student->department ?? '') }}" class="w-full border-gray-300 rounded-lg">
            @error('department')<p class="text-red-600 text-sm">{{ $message }}</p>@enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Program</label>
            <input type="text" name="program" value="{{ old('program', $student->program ?? '') }}" class="w-full border-gray-300 rounded-lg">
            @error('program')<p class="text-red-600 text-sm">{{ $message }}</p>@enderror
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium mb-1">Year of Enrollment</label>
            <input type="number" name="year_of_enrollment" value="{{ old('year_of_enrollment', $student->year_of_enrollment ?? '') }}" class="w-full border-gray-300 rounded-lg" min="2000" max="2100">
            @error('year_of_enrollment')<p class="text-red-600 text-sm">{{ $message }}</p>@enderror
        </div>
        <div>
            <label class="block text-sm font-medium mb-1">Index Number</label>
            <input type="text" name="index_number" value="{{ old('index_number', $student->index_number ?? '') }}" class="w-full border-gray-300 rounded-lg">
            @error('index_number')<p class="text-red-600 text-sm">{{ $message }}</p>@enderror
        </div>
    </div>
    <div class="flex justify-end gap-3">
        <a href="{{ route('students.index') }}" class="px-4 py-2 bg-gray-200 rounded-lg">Cancel</a>
        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Save</button>
    </div>
</div>
