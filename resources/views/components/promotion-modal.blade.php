@props(['positions' => ['Dean', 'Exam Officer', 'Program Coordinator', 'SAA', 'Faculty Officer', 'HOD']])

<div x-data="{
    show: false,
    form: {
        name: '',
        position: '',
        year_of_promotion: '',
        end_of_promotion: ''
    },
    errors: {},
    loading: false,
    
    async submit() {
        this.loading = true;
        try {
            const response = await fetch('{{ route('promotions.store') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(this.form)
            });
            
            const data = await response.json();
            
            if (response.ok) {
                window.location.reload();
            } else {
                this.errors = data.errors || {};
                // Scroll to first error
                const firstError = Object.keys(this.errors)[0];
                if (firstError) {
                    document.querySelector(`[name="${firstError}"]`).scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }
        } catch (error) {
            console.error('Error:', error);
            flash().error('An error occurred. Please try again.');
        } finally {
            this.loading = false;
        }
    }
}" x-cloak>
    <!-- Trigger Button -->
    <button 
        @click="show = true"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
    >
        Add Promotion
    </button>

    <!-- Modal -->
    <div 
        x-show="show" 
        class="fixed inset-0 z-50 overflow-y-auto" 
        aria-labelledby="modal-title" 
        role="dialog" 
        aria-modal="true"
    >
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div 
                x-show="show" 
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition-leave="opacity-100"
                x-transition-leave-end="opacity-0"
                class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                @click="show = false"
            ></div>

            <!-- Modal panel -->
            <div 
                x-show="show"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition-leave="opacity-100 translate-y-0 sm:scale-100"
                x-transition-leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
            >
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="modal-title">
                                Add New Promotion
                            </h3>
                            
                            <form @submit.prevent="submit" class="space-y-4">
                                <!-- Name -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        x-model="form.name"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                        required
                                    >
                                    <p x-show="errors.name" x-text="errors.name[0]" class="mt-1 text-sm text-red-600"></p>
                                </div>

                                <!-- Position -->
                                <div>
                                    <label for="position" class="block text-sm font-medium text-gray-700">Position</label>
                                    <select
                                        id="position"
                                        name="position"
                                        x-model="form.position"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                        required
                                    >
                                        <option value="">Select Position</option>
                                        @foreach($positions as $position)
                                            <option value="{{ $position }}">{{ $position }}</option>
                                        @endforeach
                                    </select>
                                    <p x-show="errors.position" x-text="errors.position[0]" class="mt-1 text-sm text-red-600"></p>
                                </div>

                                <!-- Year of Promotion -->
                                <div>
                                    <label for="year_of_promotion" class="block text-sm font-medium text-gray-700">Start Date</label>
                                    <input
                                        type="date"
                                        id="year_of_promotion"
                                        name="year_of_promotion"
                                        x-model="form.year_of_promotion"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                        required
                                    >
                                    <p x-show="errors.year_of_promotion" x-text="errors.year_of_promotion[0]" class="mt-1 text-sm text-red-600"></p>
                                </div>

                                <!-- End of Promotion -->
                                <div>
                                    <label for="end_of_promotion" class="block text-sm font-medium text-gray-700">End Date</label>
                                    <input
                                        type="date"
                                        id="end_of_promotion"
                                        name="end_of_promotion"
                                        x-model="form.end_of_promotion"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                        required
                                    >
                                    <p x-show="errors.end_of_promotion" x-text="errors.end_of_promotion[0]" class="mt-1 text-sm text-red-600"></p>
                                </div>

                                <!-- Form Actions -->
                                <div class="mt-6 flex justify-end space-x-3">
                                    <button
                                        type="button"
                                        @click="show = false"
                                        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        :disabled="loading"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                        :disabled="loading"
                                    >
                                        <span x-show="!loading">Save</span>
                                        <span x-show="loading" class="flex items-center justify-center">
                                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Saving...
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
