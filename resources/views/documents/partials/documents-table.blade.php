@php /** @var \Illuminate\Pagination\LengthAwarePaginator $documents */ @endphp
@if($documents->count() > 0)
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left">
                        <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded By</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Downloads</th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="documentsTableBody">
                @foreach($documents as $document)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <input type="checkbox" value="{{ $document->id }}" class="document-checkbox rounded border-gray-300">
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <i class="{{ $document->file_icon }} mr-3 text-lg"></i>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $document->name }}</div>
                                    <div class="text-sm text-gray-500">{{ strtoupper($document->file_type) }} • {{ $document->file_size }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                📁 {{ $categories[$document->category] ?? $document->category }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $document->user->name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $document->created_at->format('M j, Y') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ number_format($document->download_count) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex justify-center items-center space-x-3">
                                @if($document->file_type === 'pdf')
                                    <a href="{{ route('documents.preview', $document) }}" target="_blank" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200" title="Preview Document">
                                        <i class="fas fa-eye mr-1"></i> Preview
                                    </a>
                                @endif
                                <a href="{{ route('documents.download', $document) }}" target="_blank" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200" title="Download Document">
                                    <i class="fas fa-download mr-1"></i> Download
                                </a>
                                <button onclick="deleteDocument({{ $document->id }})" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200" title="Delete Document">
    <i class="fas fa-trash mr-1"></i> Delete
</button>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="px-6 py-4 border-t border-gray-200" id="paginationContainer">
        {{ $documents->links() }}
    </div>
@else
    <div class="text-center py-12">
        <i class="fas fa-folder-open text-gray-400 text-6xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
        <p class="text-gray-500">Upload your first document to get started.</p>
    </div>
@endif
