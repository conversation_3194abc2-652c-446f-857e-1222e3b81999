@extends('layouts.app')

@section('main')
<div class="min-h-screen bg-gray-50 rounded-xl shadow-sm p-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Document Tracker</h1>
            <p class="mt-2 text-gray-600">Manage and organize your documents efficiently</p>
        </div>

        <!-- Flash Messages -->
        <div class="mb-6">
            <x-flasher-messages />
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-alt text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Documents</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $documents->total() }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-download text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Downloads</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $documents->sum('download_count') }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-folder text-yellow-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ count($categories) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-upload text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">This Month</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    {{ $documents->where('created_at', '>=', now()->startOfMonth())->count() }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Upload Documents</h3>
            </div>
            <div class="p-6">
                <form id="uploadForm" enctype="multipart/form-data">
                    @csrf
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Select Files</label>
                            <input type="file" name="files[]" id="fileInput" multiple 
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                                   class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <p class="mt-1 text-xs text-gray-500">Supported: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX (Max: 10MB each)</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select name="category" id="categorySelect" required 
                                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Category</option>
                                @foreach($categories as $key => $label)
                                    <option value="{{ $key }}">📁 {{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" id="uploadBtn" 
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-upload mr-2"></i>
                            Upload Documents
                        </button>
                    </div>
                    <div id="uploadProgress" class="hidden mt-4">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <div class="relative">
                            <input type="text" id="dynamicSearch" 
                                   placeholder="Search by document name, category, or uploader..." 
                                   class="w-full pl-10 pr-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <div id="searchSpinner" class="absolute inset-y-0 right-0 pr-3 flex items-center hidden">
                                <i class="fas fa-spinner fa-spin text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <div>
                        <select id="categoryFilter" 
                                class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Categories</option>
                            @foreach($categories as $key => $label)
                                <option value="{{ $key }}">📁 {{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Documents</h3>
                <div class="flex items-center space-x-2">
                    <button id="bulkDownloadBtn" class="hidden inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-download mr-2"></i>
                        Download Selected
                    </button>
                    <span id="documentCount" class="text-sm text-gray-500">{{ $documents->total() }} documents</span>
                </div>
            </div>

            <div id="documentsContainer">
                @if($documents->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">Document</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider"> By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">Downloads</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500  tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="documentsTableBody">
                                @foreach($documents as $document)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4">
                                            <input type="checkbox" name="selected_documents[]" value="{{ $document->id }}" 
                                                   class="document-checkbox rounded border-gray-300">
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center">
                                                <i class="{{ $document->file_icon }} mr-3 text-lg"></i>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">{{ $document->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ strtoupper($document->file_type) }} • {{ $document->file_size }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                📁 {{ $categories[$document->category] ?? $document->category }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $document->user->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $document->created_at->format('M j, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ number_format($document->download_count) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="flex justify-center items-center space-x-3">
                                                @if($document->file_type === 'pdf')
                                                    <a href="{{ route('documents.preview', $document) }}" target="_blank"
                                                       class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200" 
                                                       title="Preview Document">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        Preview
                                                    </a>
                                                @endif
                                                <a href="{{ route('documents.download', $document) }}" 
                                                   class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200" 
                                                   title="Download Document">
                                                    <i class="fas fa-download mr-1"></i>
                                                    Download
                                                </a>
                                                @if($document->user_id === auth()->id() || auth()->user()->is_admin)
                                                    <button onclick="deleteDocument({{ $document->id }})" 
                                                            class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200" 
                                                            title="Delete Document">
                                                        <i class="fas fa-trash mr-1"></i>
                                                        Delete
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200" id="paginationContainer">
                        {{ $documents->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-folder-open text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
                        <p class="text-gray-500">Upload your first document to get started.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Dynamic search functionality
let searchTimeout;
const dynamicSearch = document.getElementById('dynamicSearch');
const categoryFilter = document.getElementById('categoryFilter');
const searchSpinner = document.getElementById('searchSpinner');
const documentsContainer = document.getElementById('documentsContainer');
const documentCount = document.getElementById('documentCount');

// Debounced search function
function performSearch() {
    const searchTerm = dynamicSearch.value.trim();
    const category = categoryFilter.value;
    
    searchSpinner.classList.remove('hidden');
    
    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (category) params.append('category', category);
    params.append('ajax', '1');
    
    fetch(`{{ route('documents.index') }}?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        documentsContainer.innerHTML = data.html;
        documentCount.textContent = data.count + ' documents';
        
        // Reattach event listeners for new elements
        attachEventListeners();
        
        searchSpinner.classList.add('hidden');
    })
    .catch(error => {
        console.error('Search error:', error);
        searchSpinner.classList.add('hidden');
    });
}

// Search input event listener
dynamicSearch.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(performSearch, 300); // 300ms delay
});

// Category filter event listener
categoryFilter.addEventListener('change', performSearch);

// Function to attach event listeners to dynamically loaded content
function attachEventListeners() {
    // Reattach bulk selection functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const documentCheckboxes = document.querySelectorAll('.document-checkbox');
    const bulkDownloadBtn = document.getElementById('bulkDownloadBtn');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            documentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleBulkActions();
        });
    }

    documentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkActions);
    });
}

// Upload functionality
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const uploadBtn = document.getElementById('uploadBtn');
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.bg-blue-600');
    
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
    progressDiv.classList.remove('hidden');
    
    fetch('{{ route("documents.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // Reload to trigger display of flash messages for both success and error cases
        location.reload();
    })
    .catch(error => {
        console.error('Error:', error);
        // Reload page so any server‐side flash message can be shown to the user
        location.reload();
    })
    .finally(() => {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload mr-2"></i>Upload Documents';
        progressDiv.classList.add('hidden');
    });
});

// Delete functionality
function deleteDocument(documentId) {
    fetch(`/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success toast and refresh current view
            if (typeof fireToast === 'function') fireToast('success', data.message);
            performSearch();
        } else {
            if (typeof fireToast === 'function') fireToast('error', data.message || 'Delete failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof fireToast === 'function') fireToast('error', 'Delete failed. Please try again.');
    });
}

// Bulk selection functionality
function toggleBulkActions() {
    const checkedBoxes = document.querySelectorAll('.document-checkbox:checked');
    const bulkDownloadBtn = document.getElementById('bulkDownloadBtn');
    
    if (checkedBoxes.length > 0) {
        bulkDownloadBtn.classList.remove('hidden');
    } else {
        bulkDownloadBtn.classList.add('hidden');
    }
}

// Bulk download functionality
document.addEventListener('click', function(e) {
    if (e.target.id === 'bulkDownloadBtn' || e.target.closest('#bulkDownloadBtn')) {
        const checkedBoxes = document.querySelectorAll('.document-checkbox:checked');
        const documentIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (documentIds.length === 0) {
            alert('Please select documents to download.');
            return;
        }
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("documents.bulk-download") }}';
        form.style.display = 'none';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfInput);
        
        documentIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'document_ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }
});

// Initialize event listeners on page load
document.addEventListener('DOMContentLoaded', function() {
    attachEventListeners();
});
</script>
@endpush
@endsection