<?php

use Flasher\Prime\Configuration;

return Configuration::from([
    'default' => 'flasher',

    // Automatically inject CSS & JS assets generated by php-flasher:install
    'inject_assets' => true,

    // Global notification options (can be overridden per-notification)
    'options' => [
        // How long to display each notification (in milliseconds)
        'timeout' => 500,
    ],

    // Map Laravel session flash keys to Flasher notification types
    'flash_bag' => [
        'success' => ['success'],
        'error'   => ['error', 'danger'],
        'warning' => ['warning', 'alarm'],
        'info'    => ['info', 'notice', 'alert'],
    ],
]);
